<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>kaplanco</groupId>
  <artifactId>hubspot-updateproducts</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>kaplan-update-products</name>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <exec.mainClass>com.kaplanco.hubspot.products.UpdateProducts</exec.mainClass>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.14</version>
        </dependency>
		<dependency>
		    <groupId>com.opencsv</groupId>
		    <artifactId>opencsv</artifactId>
		    <version>5.7.0</version>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.5.0</version>
		</dependency>
		<dependency>
		    <groupId>mysql</groupId>
		    <artifactId>mysql-connector-java</artifactId>
		    <version>8.0.33</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-api</artifactId>
		    <version>2.19.0</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-core</artifactId>
		    <version>2.19.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.zaxxer/HikariCP -->
		<dependency>
		    <groupId>com.zaxxer</groupId>
		    <artifactId>HikariCP</artifactId>
		    <version>5.0.1</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.schmizz/sshj -->
		<dependency>
			<groupId>net.schmizz</groupId>
			<artifactId>sshj</artifactId>
			<version>0.10.0</version>
		</dependency>
		<dependency>
		    <groupId>com.kaplanco.hubspot</groupId>
		    <artifactId>kaplan-hubspot-core</artifactId>
		    <version>1.0.0</version>
		</dependency>
	</dependencies>
	
	<build>
		<plugins>
			<plugin>
				<!--
				Creates hubspot-updateproducts-0.0.0.1-SHAPSHOT-spring-boot.jar in M2_HOME/repository/kaplanco/hubspot-updateproducts/0.0.1-SHAPSHOT folder
				The syntax to run is java -jar <jar path> "<path to properties file>"
				//-->
			    <groupId>org.springframework.boot</groupId>
			    <artifactId>spring-boot-maven-plugin</artifactId>
			    <version>3.0.6</version>
			    <executions>
			        <execution>
			            <goals>
			                <goal>repackage</goal>
			            </goals>
			            <configuration>
			                <classifier>spring-boot</classifier>
			                <mainClass>
			                  com.kaplanco.hubspot.products.UpdateProducts
			                </mainClass>
			            </configuration>
			        </execution>
			    </executions>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>
	</build>
</project>