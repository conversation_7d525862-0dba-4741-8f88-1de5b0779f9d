package com.kaplanco.hubspot.products.api;

import java.util.HashMap;
import java.util.Map;

public class HubSpotApiResult
{
	private String id;
	private Map< String, String > properties;
	private String createdAt;
	private String updatedAt;
	private Boolean archived;

	public String getId()
	{
		return id;
	}

	public void setId( final String id )
	{
		this.id = id;
	}

	public Map< String, String > getProperties()
	{
		return properties;
	}

	public void setProperties( final Map< String, String > properties )
	{
		this.properties = properties;
	}

	public String getCreatedAt()
	{
		return createdAt;
	}

	public void setCreatedAt( final String createdAt )
	{
		this.createdAt = createdAt;
	}

	public String getUpdatedAt()
	{
		return updatedAt;
	}

	public void setUpdatedAt( final String updatedAt )
	{
		this.updatedAt = updatedAt;
	}

	public Boolean getArchived()
	{
		return archived;
	}

	public void setArchived( final Boolean archived )
	{
		this.archived = archived;
	}

	public void setProperty( final String propertyName, final String propertyValue )
	{
		if ( properties == null )
		{
			properties = new HashMap< String, String >();
		}

		properties.put( propertyName, propertyValue );
	}

	public String getProperty( final String propertyName )
	{
		String value = null;
		if ( properties != null )
		{
			value = properties.get( propertyName );
		}

		return value;
	}

	public void deleteProperty( final String propertyName )
	{
		properties.remove( propertyName );
	}
}
