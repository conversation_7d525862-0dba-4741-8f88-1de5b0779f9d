-- DROP FUNCTION products.check_hubspot_differences(int8);

CREATE OR REPLACE FUNCTION products.check_hubspot_differences(jobid bigint)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
	BEGIN
		
		DROP TABLE IF EXISTS tmp_kap_products;
		DROP TABLE IF EXISTS tmp_gh_products;
		
		CREATE TEMP TABLE tmp_kap_products AS
		SELECT ip.job_id, ip.product_id, ip.product_name, ip.product_image, ip.product_image_url, ip.product_url, ip.description, 
						ip.truck_sw, ip.drop_ship_sw, ip.retail_price, ip.sale_price, ip.qty_on_hand, ip.discountable_sw, 
						ip.kaplan_exclusive, ip.nla, ip.to_be_nla, ip.second_day_up_charge, ip.next_day_up_charge, ip.in_stock_date, 
						ip.ps_add_date, ip.is_kaplan_product, ip.kaplan_product_id, ip.product_cost, ip.isbn 
		FROM products.imp_products ip 
		WHERE ip.job_id = jobId 
			AND ip.is_kaplan_product = 'Y';
			
		CREATE TEMP TABLE tmp_gh_products AS	
		SELECT ip.job_id, ip.product_id, ip.product_name, ip.product_image, ip.product_image_url, ip.product_url, ip.description, 
						ip.truck_sw, ip.drop_ship_sw, ip.retail_price, ip.sale_price, ip.qty_on_hand, ip.discountable_sw, 
						ip.kaplan_exclusive, ip.nla, ip.to_be_nla, ip.second_day_up_charge, ip.next_day_up_charge, ip.in_stock_date, 
						ip.ps_add_date, ip.is_kaplan_product, ip.kaplan_product_id, ip.product_cost, ip.isbn  
		FROM products.imp_products ip 
		WHERE ip.job_id = jobId 
			AND ip.is_kaplan_product = 'N';
			
		--Insert kaplan products	
		INSERT INTO products.hubspot_products (job_id, product_id, product_name, product_image, product_image_url, product_url, description, truck_sw,
								drop_ship_sw, retail_price, sale_price, qty_on_hand, discountable_sw, kaplan_exclusive, nla, to_be_nla,
								second_day_up_charge, next_day_up_charge, in_stock_date, ps_add_date, is_kaplan_product, 
								kaplan_product_id, product_action)
		SELECT DISTINCT ip.job_id, ip.product_id, ip.product_name, ip.product_image, ip.product_image_url, ip.product_url, ip.description, 
						ip.truck_sw, ip.drop_ship_sw, ip.retail_price, ip.sale_price, ip.qty_on_hand, ip.discountable_sw, 
						ip.kaplan_exclusive, ip.nla, ip.to_be_nla, ip.second_day_up_charge, ip.next_day_up_charge, ip.in_stock_date, 
						ip.ps_add_date, ip.is_kaplan_product, ip.kaplan_product_id, 'CREATED', ip.product_cost, ip.isbn
		FROM tmp_kap_products ip
		WHERE btrim(ip.product_id) NOT IN
			(
				SELECT DISTINCT btrim(p.product_id) 
				FROM products.hubspot_products p 
				WHERE p.is_kaplan_product = ip.is_kaplan_product 
			)
			AND ip.job_id = jobId;
			
		--Insert Gryphon house products
		INSERT INTO products.hubspot_products (job_id, product_id, product_name, product_image, product_image_url, product_url, description, truck_sw,
								drop_ship_sw, retail_price, sale_price, qty_on_hand, discountable_sw, kaplan_exclusive, nla, to_be_nla,
								second_day_up_charge, next_day_up_charge, in_stock_date, ps_add_date, is_kaplan_product, 
								kaplan_product_id, product_action)
		SELECT DISTINCT ip.job_id, ip.product_id, ip.product_name, ip.product_image, ip.product_image_url, ip.product_url, ip.description, 
						ip.truck_sw, ip.drop_ship_sw, ip.retail_price, ip.sale_price, ip.qty_on_hand, ip.discountable_sw, 
						ip.kaplan_exclusive, ip.nla, ip.to_be_nla, ip.second_day_up_charge, ip.next_day_up_charge, ip.in_stock_date, 
						ip.ps_add_date, ip.is_kaplan_product, ip.kaplan_product_id, 'CREATED', ip.product_cost, ip.isbn
		FROM tmp_gh_products ip
		WHERE btrim(ip.product_id) NOT IN
			(
				SELECT DISTINCT btrim(p.product_id) 
				FROM products.hubspot_products p 
				WHERE p.is_kaplan_product = ip.is_kaplan_product 
			)
			AND ip.job_id = jobId;
			
		--Update Kaplan products
		UPDATE products.hubspot_products p 
		SET product_name = ip.product_name,
			product_image = ip.product_image,
			product_image_url = ip.product_image_url,
			product_url = ip.product_url,
			description = ip.description,
			truck_sw = ip.truck_sw,
			drop_ship_sw = ip.drop_ship_sw,
			retail_price = ip.retail_price,
			sale_price = ip.sale_price,
			qty_on_hand = ip.qty_on_hand,
			discountable_sw = ip.discountable_sw,
			nla = ip.nla,
			to_be_nla = ip.to_be_nla,
			second_day_up_charge = ip.second_day_up_charge,
			next_day_up_charge = ip.next_day_up_charge,
			in_stock_date = ip.in_stock_date,
			ps_add_date = ip.ps_add_date,
			product_cost = ip.product_cost,
			isbn = ip.isbn,
			job_id = jobId,
			product_action = 'UPDATE'
		FROM tmp_kap_products ip
		WHERE p.is_kaplan_product = ip.is_kaplan_product 
			AND p.product_id = ip.product_id
			AND ip.job_id  = jobId
			AND 
			(
				btrim(p.product_name) <> btrim(ip.product_name) 
				OR btrim(p.product_image) <> btrim(ip.product_image) 
				OR btrim(p.product_image_url) <> btrim(ip.product_image_url) 
				OR btrim(p.product_url) <> btrim(ip.product_url) 
				OR btrim(p.description) <> btrim(ip.description)
				OR btrim(p.truck_sw) <> btrim(ip.truck_sw)
				OR btrim(p.drop_ship_sw) <> btrim(ip.drop_ship_sw)
				OR btrim(p.retail_price) <> btrim(ip.retail_price)
				OR btrim(p.sale_price) <> btrim(ip.sale_price)
				OR btrim(p.qty_on_hand) <> btrim(ip.qty_on_hand)
				OR btrim(p.discountable_sw) <> btrim(ip.discountable_sw)
				OR btrim(p.nla) <> btrim(ip.nla)
				OR btrim(p.to_be_nla) <> btrim(ip.to_be_nla)
				OR btrim(p.second_day_up_charge) <> btrim(ip.second_day_up_charge)
				OR btrim(p.next_day_up_charge) <> btrim(ip.next_day_up_charge)
				OR btrim(p.in_stock_date) <> btrim(ip.in_stock_date)
				OR btrim(p.ps_add_date) <> btrim(ip.ps_add_date)
				OR btrim(p.product_cost) <> btrim(ip.product_cost)
				OR btrim(p.isbn) <> btrim(ip.isbn)
			);
			
		--Update Gryphon House Products
		UPDATE products.hubspot_products p 
		SET product_name = ip.product_name,
			truck_sw = ip.truck_sw,
			drop_ship_sw = ip.drop_ship_sw,
			retail_price = ip.retail_price,
			sale_price = ip.sale_price,
			qty_on_hand = ip.qty_on_hand,
			discountable_sw = ip.discountable_sw,
			nla = ip.nla,
			to_be_nla = ip.to_be_nla,
			second_day_up_charge = ip.second_day_up_charge,
			next_day_up_charge = ip.next_day_up_charge,
			in_stock_date = ip.in_stock_date,
			ps_add_date = ip.ps_add_date,
			product_cost = ip.product_cost,
			isbn = ip.isbn,
			job_id = jobId,
			product_action = 'UPDATE'
		FROM tmp_gh_products ip
		WHERE p.is_kaplan_product = ip.is_kaplan_product 
			AND p.product_id = ip.product_id
			AND ip.job_id  = jobId
			AND 
			(
				btrim(p.product_name) <> btrim(ip.product_name) 
				OR btrim(p.truck_sw) <> btrim(ip.truck_sw)
				OR btrim(p.drop_ship_sw) <> btrim(ip.drop_ship_sw)
				OR btrim(p.retail_price) <> btrim(ip.retail_price)
				OR btrim(p.sale_price) <> btrim(ip.sale_price)
				OR btrim(p.qty_on_hand) <> btrim(ip.qty_on_hand)
				OR btrim(p.discountable_sw) <> btrim(ip.discountable_sw)
				OR btrim(p.nla) <> btrim(ip.nla)
				OR btrim(p.to_be_nla) <> btrim(ip.to_be_nla)
				OR btrim(p.second_day_up_charge) <> btrim(ip.second_day_up_charge)
				OR btrim(p.next_day_up_charge) <> btrim(ip.next_day_up_charge)
				OR btrim(p.in_stock_date) <> btrim(ip.in_stock_date)
				OR btrim(p.ps_add_date) <> btrim(ip.ps_add_date)
				OR btrim(p.product_cost) <> btrim(ip.product_cost)
				OR btrim(p.isbn) <> btrim(ip.isbn)
			);
			
		DROP TABLE tmp_kap_products;
		DROP TABLE tmp_gh_products;
	
		RETURN 1;
	END;
$function$
;
