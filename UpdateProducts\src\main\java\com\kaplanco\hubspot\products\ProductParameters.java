package com.kaplanco.hubspot.products;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import com.kaplanco.hubspot.CommonImportParameters;

public class ProductParameters extends CommonImportParameters
{
	private FileWriter csvCreatedProducts;

	public ProductParameters()
	{
		super();
	}

	public ProductParameters( final Connection connection )
	{
		super( connection );
	}

	public ProductParameters(	final Connection connection,
									final Long jobId,
									final ResultSet resultSet ) throws SQLException
	{
		super( connection, jobId, resultSet );
	}

	public void initializeCsvCreatedProductsFile(	final String _csvFilePath,
																final String[] csvHeaderColumnNames ) throws IOException
	{
		csvCreatedProducts = new FileWriter( _csvFilePath );
		Boolean addComma = false;
		for ( final String columnName : csvHeaderColumnNames )
		{
			if ( addComma )
			{
				csvCreatedProducts.append( "," );
			}
			else
			{
				addComma = true;
			}
			csvCreatedProducts.append( columnName );
		}
		csvCreatedProducts.append( "\n" );
	}

	public void finializeCsvCreatedProductsFile() throws IOException
	{
		csvCreatedProducts.flush();
		csvCreatedProducts.close();
	}

	public void writeToCsvCreatedProductsFile( final String line ) throws IOException
	{
		csvCreatedProducts.append( line );
	}
}
