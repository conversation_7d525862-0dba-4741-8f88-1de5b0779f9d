package com.kaplanco.hubspot.products.imp;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;

/* Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this
 * license Click
 * nbfs://nbhost/SystemFileSystem/Templates/Project/Maven2/JavaApp/src/main/java/${packagePath}/${
 * mainClassName}.java to edit this template */

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kaplanco.hubspot.products.HubSpotPaging;
import com.kaplanco.hubspot.products.HubSpotProduct;
import com.kaplanco.hubspot.products.HubSpotProducts;

/**
 * <AUTHOR>
 */
public class MoveFileToFolder
{

	// private static String APP_API_CONNECTION_DEV = "********************************************";
	private static String APP_API_CONNECTION_PROD = "********************************************";

	private static HubSpotProducts processResponse( final DefaultHttpClient httpClient,
																	final HttpResponse response )
	{
		HubSpotProducts products = null;
		try
		{
			if ( response.getStatusLine().getStatusCode() != 200 )
			{
				throw new RuntimeException( "Failed : HTTP error code : "
					+ response.getStatusLine().getStatusCode() );
			}

			final InputStreamReader isr = new InputStreamReader( response.getEntity().getContent() );
			BufferedReader br;
			br = new BufferedReader( isr );

			String output;
			// System.out.println("Output from Server .... \n");
			final StringBuilder sb = new StringBuilder();
			while ( ( output = br.readLine() ) != null )
			{
				sb.append( output );
				// System.out.println(output);
			}

			final ObjectMapper mapper = new ObjectMapper();

			final String json = sb.toString();

			products = mapper.readValue( json, HubSpotProducts.class );

			httpClient.getConnectionManager().shutdown();
		}
		catch ( final IOException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}

		return products;
	}

	private static void moveProductsToFolder( final HubSpotProducts hsps, final String folderId )
	{
		final DefaultHttpClient httpClient = new DefaultHttpClient();
		final String url = "https://api.hubapi.com/crm/v3/objects/products/batch/update";

		System.out.println( "URL:" + url );
		final HttpPost postRequest = new HttpPost( url );
		postRequest.addHeader( "accept", "application/json" );
		postRequest
			.addHeader( "authorization", "Bearer " + MoveFileToFolder.APP_API_CONNECTION_PROD );

		String json = "{\"inputs\":[";

		final Collection< HubSpotProduct > hsProductColl = hsps.getResults();
		boolean addColumn = false;
		for ( final HubSpotProduct hsp : hsProductColl )
		{
			if ( addColumn )
			{
				json += ",";
			}
			else
			{
				addColumn = true;
			}

			json += "{";
			json += "\"id\":\"" + hsp.getId() + "\"";
			json += ",\"properties\":";
			json += "{\"hs_folder_id\":\"" + folderId + "\"}";
			json += "}";
		}
		json += "]}";

		System.out.println( "Product update JSON:" + json );
		try
		{
			StringEntity input;
			input = new StringEntity( json );
			input.setContentType( "application/json" );
			postRequest.setEntity( input );

			final HttpResponse response = httpClient.execute( postRequest );

			if ( response.getStatusLine().getStatusCode() != 200 )
			{
				throw new RuntimeException( "Failed : HTTP error code : "
					+ response.getStatusLine().getStatusCode() );
			}

			final InputStreamReader isr = new InputStreamReader( response.getEntity().getContent() );

			/* String output; System.out.println("Output from Server .... \n"); StringBuilder sb = new
			 * StringBuilder(); while ((output = br.readLine()) != null) { sb.append(output);
			 * System.out.println(output); } */
			httpClient.getConnectionManager().shutdown();

		}
		catch ( final UnsupportedEncodingException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
		catch ( final IOException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
	}

	public void moveProductsNotInFolderToFolder( final String businessUnit, final String folderId )
	{
		HubSpotProducts hsps = searchForProductsByBusinessUnitNotInFolder( businessUnit );

		int productsFound = hsps.getResults().size();

		int numberOfCalls = 0;
		while ( productsFound > 0 )
		{
			if ( hsps != null )
			{
				String msg = "Total products left to move:";
				final Integer totalLeft = hsps.getTotal();
				if ( totalLeft == null )
				{
					msg += "0";
				}
				else
				{
					msg += hsps.getTotal().toString();
				}

				System.out.println( msg );
			}

			numberOfCalls++ ;
			if ( numberOfCalls > 125 )
			{
				try
				{
					numberOfCalls = 0;
					Thread.sleep( 10000 );
				}
				catch ( final InterruptedException ex )
				{
					Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
				}
			}

			moveProductsToFolder( hsps, folderId );

			hsps = searchForProductsByBusinessUnitNotInFolder( businessUnit );
			productsFound = hsps.getResults().size();
		}
	}

	/**
	 * Finds product by name for the KELC Business Unit that are not in a folder
	 *
	 * @param productName - The name of the product to search for
	 */
	private static void testHubSpotProductSearchByName( final String productName )
	{
		final DefaultHttpClient httpClient = new DefaultHttpClient();
		final String url = "https://api.hubapi.com/crm/v3/objects/products/search";

		System.out.println( "URL:" + url );
		final HttpPost postRequest = new HttpPost( url );
		postRequest.addHeader( "accept", "application/json" );
		postRequest
			.addHeader( "authorization", "Bearer " + MoveFileToFolder.APP_API_CONNECTION_PROD );

		String json = "{\"filterGroups\":[";
		json += "{\"filters\":[";
		json += "{\"propertyName\":\"business_unit\",\"operator\":\"EQ\",\"value\":\"KELC\"}";
		json += ",{\"propertyName\":\"hs_folder_id\",\"operator\":\"NOT_HAS_PROPERTY\"}";
		json += ",{\"propertyName\":\"name\",\"operator\":\"EQ\",\"value\":\"" + productName + "\"}";
		json += "]}]";
		json += ",\"properties\":[\"hs_folder_id,name\"]";
		json += ",\"limit\":10,\"after\":0";
		/* json += ",\"sorts\":[null]"; json += ",\"properties\":[\"hs_folder_id,name\"]"; json +=
		 * ",\"limit\":10,\"after\":0"; */
		json += "}";
		System.out.println( "JSON:" + json );

		try
		{
			StringEntity input;
			input = new StringEntity( json );
			input.setContentType( "application/json" );
			postRequest.setEntity( input );

			final HttpResponse response = httpClient.execute( postRequest );
			final HubSpotProducts hsps = processResponse( httpClient, response );
		}
		catch ( final UnsupportedEncodingException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
		catch ( final IOException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
	}

	/**
	 * Finds all products for the KELC Business Unit which are not in a folder
	 */
	private static HubSpotProducts searchForProductsByBusinessUnitNotInFolder( final String businessUnit )
	{
		final DefaultHttpClient httpClient = new DefaultHttpClient();
		// final String url = "https://api.hubapi.com/crm/v3/objects/products";
		final String url = "https://api.hubapi.com/crm/v3/objects/products/search";

		// System.out.println("URL:" + url);
		final HttpPost postRequest = new HttpPost( url );
		postRequest.addHeader( "accept", "application/json" );
		postRequest
			.addHeader( "authorization", "Bearer " + MoveFileToFolder.APP_API_CONNECTION_PROD );

		String json = "{\"filterGroups\":[";
		json += "{\"filters\":[";
		json += "{\"propertyName\":\"business_unit\",\"operator\":\"EQ\",\"value\":\"" + businessUnit
			+ "\"}";
		json += ",{\"propertyName\":\"hs_folder_id\",\"operator\":\"NOT_HAS_PROPERTY\"}";
		json += "]}]";
		json += ",\"properties\":[\"hs_folder_id,name\"]";
		json += ",\"limit\":100,\"after\":0";
		/* json += ",\"sorts\":[null]"; json += ",\"properties\":[\"hs_folder_id,name\"]"; json +=
		 * ",\"limit\":10,\"after\":0"; */
		json += "}";
		// System.out.println("JSON:" + json);

		HubSpotProducts hsps = null;
		try
		{
			StringEntity input;
			input = new StringEntity( json );
			input.setContentType( "application/json" );
			postRequest.setEntity( input );

			final HttpResponse response = httpClient.execute( postRequest );
			hsps = processResponse( httpClient, response );
		}
		catch ( final UnsupportedEncodingException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
		catch ( final IOException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}

		return hsps;
	}

	private static void testHubSpotProductList()
	{
		final DefaultHttpClient httpClient = new DefaultHttpClient();
		String url = "https://api.hubapi.com/crm/v3/objects/products?";
		url += "limit=10";
		url += "&properties=name";
		url += "&properties=hs_folder_id";
		url += "&properties=hs_sku";
		url += "&properties=business_unit";
		url += "&archived=false";
		url += "&business_unit=KELC";

		System.out.println( "URL:" + url );
		final HttpGet getRequest = new HttpGet( url );
		getRequest.addHeader( "accept", "application/json" );
		getRequest.addHeader( "authorization", "Bearer " + MoveFileToFolder.APP_API_CONNECTION_PROD );

		try
		{
			final HttpResponse response = httpClient.execute( getRequest );
			final HubSpotProducts hsps = processResponse( httpClient, response );
		}
		catch ( final IOException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
	}

	private static void testObjectToJson()
	{
		final Map< String, String > hsPropertiesMap = new HashMap< String, String >();
		hsPropertiesMap.put( "createdate", "2202-08-30" );
		hsPropertiesMap.put( "hs_lastmodifieddate", "2202-08-30" );
		hsPropertiesMap.put( "hs_object_id", "111111111111" );
		hsPropertiesMap.put( "name", "Product 1 Name" );

		final HubSpotProduct hsp = new HubSpotProduct();
		hsp.setId( "111111111" );
		hsp.setProperties( hsPropertiesMap );
		hsp.setCreatedAt( "777777" );
		hsp.setUpdatedAt( "88888888" );
		hsp.setArchived( false );

		final Collection< HubSpotProduct > hsProductColl = new ArrayList< HubSpotProduct >();
		hsProductColl.add( hsp );

		final Map< String, String > hsPropertiesMap2 = new HashMap< String, String >();
		hsPropertiesMap2.put( "createdate", "2202-08-30" );
		hsPropertiesMap2.put( "hs_lastmodifieddate", "2202-08-30" );
		hsPropertiesMap2.put( "hs_object_id", "2222222222222" );
		hsPropertiesMap2.put( "name", "Product 2 Name" );

		final HubSpotProduct hsp2 = new HubSpotProduct();
		hsp2.setId( "2222222222222" );
		hsp2.setProperties( hsPropertiesMap2 );
		hsp2.setCreatedAt( "777777" );
		hsp2.setUpdatedAt( "88888888" );
		hsp2.setArchived( false );

		hsProductColl.add( hsp2 );

		final HubSpotProducts hsps = new HubSpotProducts();
		hsps.setResults( hsProductColl );

		final HubSpotPaging hsPagingMap = new HubSpotPaging();
		final Map< String, String > next = new HashMap< String, String >();
		next.put( "after", "88888888888888" );
		next.put( "link", "www.hubspot.com" );
		hsPagingMap.setNext( next );

		hsps.setPaging( hsPagingMap );

		final ObjectMapper mapper = new ObjectMapper();
		try
		{
			System.out.println( "Object to Json" );
			final String jsonString = mapper.writeValueAsString( hsps );
			System.out.print( jsonString );

			// See if you can map JSon back to object
			final HubSpotProducts hsps2 = mapper.readValue( jsonString, HubSpotProducts.class );
		}
		catch ( final JsonProcessingException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
		catch ( final IOException ex )
		{
			Logger.getLogger( MoveFileToFolder.class.getName() ).log( Level.SEVERE, null, ex );
		}
	}

	public static void main( final String[] args )
	{
		// Simple get a list of products
		// testHubSpotProductList();

		// Convert
		// testObjectToJson();

		// HubSpotProducts hsps = testHubSpotProductSearch();
		// testHubSpotProductBatchUpdate(hsps);

		// KELC folder Id: 9647682
		// Business Unit: KELC
		// Gryphon House folder Id: 9647683
		// Business Unit : Gryphon House

		final MoveFileToFolder moveFiles = new MoveFileToFolder();

		// Find folder id from inspecting
		final String folderId = "7544943";
		moveFiles.moveProductsNotInFolderToFolder( "KELC", folderId );

		// testHubSpotProductSearchById("1606244732");
		// testHubSpotProductSearchByName("Liquid Motion Bubble Timers - Set of 3");
	}
}
