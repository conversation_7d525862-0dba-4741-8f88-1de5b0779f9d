package com.kaplanco.hubspot.products;

public class ImpProductEntry
{
	private Long jobId;
	private String productID;
	private String productName;
	private String productImage;
	private String productImageUrl;
	private String productUrl;
	private String description;
	private String truckSw;
	private String dropShipSw;
	private String retailPrice;
	private String salePrice;
	private String qtyOnHand;
	private String discountableSw;
	private String kaplanExclusive;
	private String nla;
	private String toBeNla;
	private String secondDayUpCharge;
	private String nextDayUpCharge;
	private String inStockDate;
	private String psAddDate;
	private String isKaplanProduct;
	private String kaplanProductID;
	private String cost;
	private String isbn;
	private String weight;

	public Long getJobId()
	{
		return jobId;
	}

	public void setJobId( Long jobId )
	{
		this.jobId = jobId;
	}

	public String getProductID()
	{
		return productID;
	}

	public void setProductID( String productID )
	{
		this.productID = productID;
	}

	public String getProductName()
	{
		return productName;
	}

	public void setProductName( String productName )
	{
		this.productName = productName;
	}

	public String getProductImage()
	{
		return productImage;
	}

	public void setProductImage( String productImage )
	{
		this.productImage = productImage;
	}

	public String getProductImageUrl()
	{
		return productImageUrl;
	}

	public void setProductImageUrl( String productImageUrl )
	{
		this.productImageUrl = productImageUrl;
	}

	public String getProductUrl()
	{
		return productUrl;
	}

	public void setProductUrl( String productUrl )
	{
		this.productUrl = productUrl;
	}

	public String getDescription()
	{
		return description;
	}

	public void setDescription( String description )
	{
		this.description = description;
	}

	public String getTruckSw()
	{
		return truckSw;
	}

	public void setTruckSw( String truckSw )
	{
		this.truckSw = truckSw;
	}

	public String getDropShipSw()
	{
		return dropShipSw;
	}

	public void setDropShipSw( String dropShipSw )
	{
		this.dropShipSw = dropShipSw;
	}

	public String getRetailPrice()
	{
		return retailPrice;
	}

	public void setRetailPrice( String retailPrice )
	{
		this.retailPrice = retailPrice;
	}

	public String getSalePrice()
	{
		return salePrice;
	}

	public void setSalePrice( String salePrice )
	{
		this.salePrice = salePrice;
	}

	public String getQtyOnHand()
	{
		return qtyOnHand;
	}

	public void setQtyOnHand( String qtyOnHand )
	{
		this.qtyOnHand = qtyOnHand;
	}

	public String getDiscountableSw()
	{
		return discountableSw;
	}

	public void setDiscountableSw( String discountableSw )
	{
		this.discountableSw = discountableSw;
	}

	public String getKaplanExclusive()
	{
		return kaplanExclusive;
	}

	public void setKaplanExclusive( String kaplanExclusive )
	{
		this.kaplanExclusive = kaplanExclusive;
	}

	public String getNla()
	{
		return nla;
	}

	public void setNla( String nla )
	{
		this.nla = nla;
	}

	public String getToBeNla()
	{
		return toBeNla;
	}

	public void setToBeNla( String toBeNla )
	{
		this.toBeNla = toBeNla;
	}

	public String getSecondDayUpCharge()
	{
		return secondDayUpCharge;
	}

	public void setSecondDayUpCharge( String secondDayUpCharge )
	{
		this.secondDayUpCharge = secondDayUpCharge;
	}

	public String getNextDayUpCharge()
	{
		return nextDayUpCharge;
	}

	public void setNextDayUpCharge( String nextDayUpCharge )
	{
		this.nextDayUpCharge = nextDayUpCharge;
	}

	public String getInStockDate()
	{
		return inStockDate;
	}

	public void setInStockDate( String inStockDate )
	{
		this.inStockDate = inStockDate;
	}

	public String getPsAddDate()
	{
		return psAddDate;
	}

	public void setPsAddDate( String psAddDate )
	{
		this.psAddDate = psAddDate;
	}

	public String getIsKaplanProduct()
	{
		return isKaplanProduct;
	}

	public void setIsKaplanProduct( String isKaplanProduct )
	{
		this.isKaplanProduct = isKaplanProduct;
	}

	public String getKaplanProductID()
	{
		return kaplanProductID;
	}

	public void setKaplanProductID( String kaplanProductID )
	{
		this.kaplanProductID = kaplanProductID;
	}

	public String getCost()
	{
		return cost;
	}

	public void setCost( String cost )
	{
		this.cost = cost;
	}

	public String getIsbn()
	{
		return isbn;
	}

	public void setIsbn( String isbn )
	{
		this.isbn = isbn;
	}

	public String getWeight()
	{
		return weight;
	}

	public void setWeight( String weight )
	{
		this.weight = weight;
	}

}
