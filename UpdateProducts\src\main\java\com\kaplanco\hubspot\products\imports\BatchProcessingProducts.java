package com.kaplanco.hubspot.products.imports;

import java.io.IOException;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.kaplanco.hubspot.HubSpotApiResult;
import com.kaplanco.hubspot.HubSpotCreateBatchResults;
import com.kaplanco.hubspot.MethodReturn;
import com.kaplanco.hubspot.orderdestinations.HubSpotOrderDestinationMethods;
import com.kaplanco.hubspot.products.ProductParameters;
import com.kaplanco.hubspot.products.UpdateProducts;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;

public class BatchProcessingProducts
{
	protected Logger logger = LogManager.getFormatterLogger( BatchProcessingProducts.class );

	protected final ProductParameters params;
	protected final UpdateProducts up;
	protected final String action;
	protected final HubSpotOrderDestinationMethods hsodm;

	public BatchProcessingProducts(	final ProductParameters _params,
												final UpdateProducts _up,
												final String _action )
	{
		params = _params;
		up = _up;
		action = _action;

		final var kelcProductsApiKey = up.getProperty( "KELC_PRODUCTS_API_KEY" );
		hsodm = new HubSpotOrderDestinationMethods( kelcProductsApiKey );
	}

	protected String formatJsonProperty(	final String propertyName,
														final String propertyValue,
														final Boolean addComma )
	{
		var json = "";
		if ( addComma )
		{
			json += ",";
		}

		json += "\"" + propertyName + "\": ";

		// final String regex = "[\u2018\u2019\u201A\u201B\u201C\u201D\u201E\u201F]";
		// final String filteredPropertyValue = propertyValue.replaceAll( regex, "'" );

		final String filteredPropertyValue = replaceWordCharacters( propertyValue );

		final var propertyValueWithSingleQuotesInsteadOfDoubleQuotes = filteredPropertyValue
			.replace( "\"", "'" );
		json += "\"" + propertyValueWithSingleQuotesInsteadOfDoubleQuotes + "\"";

		return json;
	}

	private String replaceWordCharacters( String input )
	{
		// Define a pattern for Microsoft Word smart quotes, apostrophes, and the specific character
		final Pattern pattern = Pattern.compile( "[“”‘’\u001A]" );
		final Matcher matcher = pattern.matcher( input );

		// Replace Microsoft Word special characters with regular quotes
		final String result = matcher.replaceAll( m ->
		{
			switch ( m.group() )
			{
				case "“":
				case "”":
					return "\"";
				case "‘":
				case "’":
					return "'";
				case "\u001A":
					return "'"; // Replace the specific character with a single quote
				default:
					return m.group();
			}
		} );

		final String filteredResult = result.replace( "\\", "" );

		return filteredResult;
	}

	private String createProductJsonFromCsvLine( final String[] csvLine,
																Boolean isKaplanProduct )	throws ParseException,
																									InterruptedException
	{
		// For UPDATE actions, check if hs_object_id is NULL/empty first
		// hubspot_id is at index 18 based on HUBSPOT_IMPORT_CSV_PRODUCTS_HEADERS
		if ( UpdateProducts.HUBSPOT_IMPORT_ACTION_UPDATE.equals( action ) == Boolean.TRUE )
		{
			final String hs_object_id = csvLine.length > 18 ? csvLine[18] : null;
			if ( hs_object_id == null || hs_object_id.trim().isEmpty() || "null".equalsIgnoreCase(hs_object_id.trim()) )
			{
				// Log this record but don't include in batch - return null to indicate filtering
				final String logMessage = "Filtering out UPDATE record with NULL hs_object_id. SKU: " +
					(csvLine.length > 1 ? csvLine[1] : "unknown") +
					", Product Name: " + (csvLine.length > 0 ? csvLine[0] : "unknown");
				logger.warn( logMessage );
				up.logJobMessage( params, logMessage );
				return null;
			}
		}

		var json = "{ \"properties\":{";

		final var propertiesToReturn = new ArrayList< String >();

		var csvColumnIndex = 0;
		json += formatJsonProperty( "name", csvLine[ csvColumnIndex ], false );
		propertiesToReturn.add( "name" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "hs_sku", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "hs_sku" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "description", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "description" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "hs_images", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "hs_images" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "hs_url", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "hs_url" );

		// Create a SimpleDateFormat for the input format
		final var inputFormat = new SimpleDateFormat( "MM-dd-yyyy" );

		// Create a SimpleDateFormat for the output format
		final var outputFormat = new SimpleDateFormat( "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" );

		// Parse the input string to a Date object
		csvColumnIndex++ ;
		var closedate = csvLine[ csvColumnIndex ];
		if ( closedate != null && !closedate.isBlank() )
		{
			final var date = inputFormat.parse( closedate );

			// Format the Date object to a string with the output format
			closedate = outputFormat.format( date );
			json += formatJsonProperty( "date_added", closedate, true );
		}
		propertiesToReturn.add( "date_added" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "drop_ship", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "drop_ship" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "kaplan_exclusive", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "kaplan_exclusive" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "n2nd_day_freight_upcharge", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "n2nd_day_freight_upcharge" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "next_day_freight_upcharge", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "next_day_freight_upcharge" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "nla", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "nla" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "to_be_nla", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "to_be_nla" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "truck", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "truck" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "price", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "price" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "sale_price", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "sale_price" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "discountable", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "discountable" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "hs_cost_of_goods_sold", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "hs_cost_of_goods_sold" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "product_isbn", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "product_isbn" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "product_weight", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "product_weight" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "quantity_on_hand", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "quantity_on_hand" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "gryphon_house_shopify_sku", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "gryphon_house_shopify_sku" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "import_job_action", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "import_job_action" );

		csvColumnIndex++ ;
		json += formatJsonProperty( "import_job_id", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "import_job_id" );

		csvColumnIndex++ ;
		final var hs_object_id = csvLine[ csvColumnIndex ];

		csvColumnIndex++ ;
		json += formatJsonProperty( "business_unit", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "business_unit" );

		/*
		json += formatJsonProperty( "hs_folder_name", csvLine[ csvColumnIndex ], true );
		propertiesToReturn.add( "hs_folder_name" );
		*/
		
		json += "}";

		if ( UpdateProducts.HUBSPOT_IMPORT_ACTION_UPDATE.equals( action ) == Boolean.TRUE )
		{
			// Extract hs_object_id from CSV line (hubspot_id is at index 18)
			final String hs_object_id = csvLine.length > 18 ? csvLine[18] : null;
			json += formatJsonProperty( "id", hs_object_id, true );
		}

		json += ",\"propertiesToReturn\": [";
		var addComma = false;
		for ( final String property : propertiesToReturn )
		{
			if ( addComma )
			{
				json += ",";
			}
			else
			{
				addComma = true;
			}

			json += "\"";
			json += property;
			json += "\"";
		}
		json += "]}";
		return json;
	}

	private MethodReturn< String > updateProductHubSpotId(	final String sku,
																				final String hubSpotId,
																				Boolean isKaplanProduct )
	{
		final var ret = new MethodReturn< String >();

		if ( sku != null && !sku.isBlank() )
		{
			// Update existing erp_orders record
			var sql = "UPDATE hubspot_products SET hubspot_id=?";
			sql += " WHERE hubspot_id IS NULL AND is_kaplan_product = ?";

			String is_kaplan_product;
			if ( isKaplanProduct )
			{
				is_kaplan_product = "Y";
				sql += " AND TRIM(LEADING 'KELC_' FROM ?) = TRIM(LEADING '0' FROM product_id)";
			}
			else
			{
				is_kaplan_product = "N";
				sql += " AND TRIM(LEADING 'GH_' FROM ?) = TRIM(LEADING '0' FROM product_id)";
			}

			try ( var statement = params.getConnection().prepareStatement( sql ) )
			{
				final Long hubspotIdLong = Long.parseLong( hubSpotId );

				statement.setLong( 1, hubspotIdLong );
				statement.setString( 2, is_kaplan_product );
				statement.setString( 3, sku );

				params.startTimeDatabase();
				statement.executeUpdate();
				params.stopTimeDatabase();

			}
			catch ( final SQLException e )
			{
				ret.setErrorCode( String.valueOf( e.getErrorCode() ) );

				up.logJobMessage( params, "updateErpOrdersProductHubSpotId:" + e.getMessage() );
				ret.setResultsMessage( e.getMessage() );
				e.printStackTrace();
			}
		}

		return ret;
	}

	private MethodReturn< String > updateProductHubSpotIds(	final HubSpotCreateBatchResults results,
																				final Integer productsInBatch,
																				Boolean isKaplanProduct ) throws IOException
	{
		var ret = new MethodReturn< String >();

		final var products = results.getResults();
		Integer productIdsUpdated = 0;
		for ( final HubSpotApiResult product : products )
		{
			final var sku = product.getProperty( "hs_sku" );
			ret = updateProductHubSpotId( sku, product.getId(), isKaplanProduct );

			final var errorMessage = ret.getResultsMessage();
			if ( errorMessage != null && !errorMessage.isBlank() )
			{
				up
					.logJobMessage(	params,
											"updateProductHubSpotIds error updating Product HubSpot ID for "
												+ sku );
			}
			else
			{
				productIdsUpdated++ ;
			}
		}

		if ( productIdsUpdated.equals( productsInBatch ) == Boolean.FALSE )
		{
			final var f = "updateProductHubSpotIds HubSpot Products created[%d] does not equal IDs updated in database[%d]";
			final var errorMsg = String.format( f, productsInBatch, productIdsUpdated );
			up.logJobMessage( params, errorMsg );
			ret.setResultsMessage( errorMsg );
		}

		return ret;
	}

	protected Map< String, String > createLogPropertiesMap( final HubSpotApiResult result )
	{
		final var ret = new HashMap< String, String >();

		ret.put( "order_no", result.getProperty( "k__" ) );
		ret.put( "order_status", result.getProperty( "peoplesoft_order_status" ) );
		ret.put( "order_date", result.getProperty( "order_date" ) );

		final var bill_to_customer_no = result.getProperty( "kelc_bill_to_customer__" );
		if ( !bill_to_customer_no.isBlank() )
		{
			final var nos = bill_to_customer_no.split( "-" );
			if ( nos != null && nos.length > 0 )
			{
				ret.put( "bill_to_cust_id", nos[ 0 ] );
				if ( nos.length > 1 )
				{
					ret.put( "bill_to_seq", nos[ 1 ] );
				}
			}
		}

		ret.put( "bill_to_hubspot_id", result.getProperty( "hubspot_id_billto_company" ) );

		// ret.put( "ship_to_hubspot_id", result.getProperty( "ship_to_hubspot_id" ) );
		ret.put( "contact_email", result.getProperty( "customer_email" ) );
		ret.put( "sales_rep_code", result.getProperty( "peoplesoft_order_sales_code" ) );
		ret.put( "sales_rep_name", result.getProperty( "peoplesoft_order_owner_name" ) );

		return ret;
	}

	protected void logErrorsToDatabase( final HubSpotCreateBatchResults results, final String json )
	{
		final var apiResults = results.getResults();
		for ( final HubSpotApiResult result : apiResults )
		{
			final var status = result.getStatus();
			if ( "UPDATED".equals( status ) == Boolean.FALSE )
			{
				final var error = result.getError();
				final var errorCode = ( Integer ) error.get( "code" );
				var message = "";
				if ( errorCode != null )
				{
					message += "(" + errorCode + "):";
				}

				var errorMessage = ( String ) error.get( "message" );
				if ( errorMessage != null )
				{
					errorMessage += ";JSON[" + json + "]";
					message += errorMessage;
				}

				final var logPropertiesMap = createLogPropertiesMap( result );
				up.logJobMessage( params, logPropertiesMap, message );
			}
		}
	}

	private MethodReturn< Integer > executeBatchCall(	final String objectTypeId,
																		final String json,
																		final Integer productsInBatch,
																		Boolean isKaplanProduct ) throws IOException
	{
		final var ret = new MethodReturn< Integer >();

		MethodReturn< HubSpotCreateBatchResults > mrResults;
		if ( UpdateProducts.HUBSPOT_IMPORT_ACTION_CREATE.equals( action ) == Boolean.TRUE )
		{
			mrResults = hsodm.batchCreate( objectTypeId, json );
		}
		else
		{
			mrResults = hsodm.batchUpdate( objectTypeId, json );
		}

		var errorMessage = mrResults.getResultsMessage();
		if ( errorMessage == null || errorMessage.isBlank() )
		{
			final var results = mrResults.getReturnValue();
			final var status = results.getStatus();
			if ( "PARTIAL".equals( status ) )
			{
				// Some of the Order Destinations where not updated
				logErrorsToDatabase( results, json );
			}
		}
		else
		{
			errorMessage = "BatchProcessingProducts.executeBatchCall " + action
				+ " 1st attempt was not successful:" + errorMessage;
			errorMessage += ";JSON[" + json + "]";
			logger.error( errorMessage );
			up.logJobMessage( params, errorMessage );

			if ( UpdateProducts.HUBSPOT_IMPORT_ACTION_CREATE.equals( action ) == Boolean.TRUE )
			{
				mrResults = hsodm.batchCreate( objectTypeId, json );
			}
			else
			{
				mrResults = hsodm.batchUpdate( objectTypeId, json );
			}

			errorMessage = mrResults.getResultsMessage();
			if ( errorMessage != null && !errorMessage.isBlank() )
			{
				errorMessage = "BatchProcessingProducts.executeBatchCall " + action
					+ " 1st attempt was not successful:" + errorMessage;
				errorMessage += ";JSON[" + json + "]";
				logger
					.error( "executeBatchCall " + action + " 2nd attempt was not successful:"
						+ errorMessage );
				up.logJobMessage( params, errorMessage );

				ret.setResultsMessage( errorMessage );
				return ret;
			}
		}

		final var results = mrResults.getReturnValue();
		final var returnedResults = results.getResults();
		final var countOfResults = returnedResults.length;
		ret.setReturnValue( countOfResults );
		if ( UpdateProducts.HUBSPOT_IMPORT_ACTION_CREATE.equals( action ) == Boolean.TRUE )
		{
			final var mrs = updateProductHubSpotIds( results, productsInBatch, isKaplanProduct );
			errorMessage = mrs.getResultsMessage();
			if ( errorMessage != null && !errorMessage.isBlank() )
			{
				errorMessage = "BatchProcessingProducts.executeBatchCall " + action + ":"
					+ errorMessage;
				errorMessage += ";JSON[" + json + "]";
				logger.error( errorMessage );
				up.logJobMessage( params, errorMessage );

				return ret;
			}
		}

		return ret;
	}

	public MethodReturn< Integer > hubSpotProductsUsingBatch(	final CSVReader csvReader,
																					Boolean isKaplanProduct ) throws InterruptedException
	{
		final var ret = new MethodReturn< Integer >();
		final var productsId = "0-7";

		// Read CSV line by line and use the string array as you want
		var csvLine = new String[ 0 ];
		var json = "{\"inputs\": [";
		Integer linesCount = 0;
		Integer totalProductsUpdated = 0;
		Integer totalResultsReturned = 0;
		Integer filteredOutCount = 0; // Track records filtered out due to NULL hs_object_id
		try
		{
			while ( ( csvLine = csvReader.readNext() ) != null )
			{
				if ( csvLine != null )
				{
					logger.trace( Arrays.toString( csvLine ) );
					if ( csvLine.length < 12 )
					{
						logger
							.error( "hubSpotProductsUsingBatch " + action
								+ ": csvLine is missing columns, skiping lines. csvLine["
								+ Arrays.toString( csvLine ) + "]" );
						continue;
					}

					// Create JSON for this line - may return null if filtered out
					final String lineJson = createProductJsonFromCsvLine( csvLine, isKaplanProduct );

					// Only add to batch if JSON was created (not filtered out)
					if ( lineJson != null )
					{
						if ( linesCount > 0 )
						{
							json += ",";
						}
						json += lineJson;
						linesCount++ ;

						if ( linesCount == UpdateProducts.HUBSPOT_ORDER_DESTINATIONS_PER_BATCH_CREATE )
						{
							json += "]}";

							final var mri = executeBatchCall( productsId, json, linesCount, isKaplanProduct );
							final var errorMessage = mri.getResultsMessage();
							if ( errorMessage != null && !errorMessage.isBlank() )
							{
								// Don't return error, just log the error and continue.
								up.logJobMessage( params, errorMessage );
							}
							else
							{
								totalResultsReturned += mri.getReturnValue();
								totalProductsUpdated += linesCount;
							}

							json = "{\"inputs\": [";
							linesCount = 0;
						}
					}
					else
					{
						// Record was filtered out due to NULL hs_object_id
						filteredOutCount++;
					}
				}
			}

			if ( linesCount > 0 )
			{
				json += "]}";

				final var mri = executeBatchCall( productsId, json, linesCount, isKaplanProduct );

				final var errorMessage = mri.getResultsMessage();
				if ( errorMessage != null && !errorMessage.isBlank() )
				{
					// Don't return error, just log the error and continue.
					up.logJobMessage( params, errorMessage );
				}
				else
				{
					// This would cause null pointer if there was an error
					totalResultsReturned += mri.getReturnValue();
				}

				totalProductsUpdated += linesCount;
			}

			if ( totalProductsUpdated.equals( totalResultsReturned ) == Boolean.FALSE )
			{
				final var f = "hubSpotProductsUsingBatch: Total Products updated[%d] does not match the Total HubSpot Results returned[%d]";
				final var errMsg = String.format( f, totalProductsUpdated, totalResultsReturned );
				up.logJobMessage( params, errMsg );
			}

			// Log summary of filtering
			if ( filteredOutCount > 0 )
			{
				final var filterMsg = String.format( "hubSpotProductsUsingBatch: Filtered out %d records with NULL hs_object_id for %s action",
					filteredOutCount, action );
				logger.info( filterMsg );
				up.logJobMessage( params, filterMsg );
			}
		}
		catch ( final CsvValidationException e )
		{
			final var format = "json[%s], csvline[%s]";
			final var msg = String.format( format, json, Arrays.toString( csvLine ) );
			ret.setResultsMessage( e.getMessage() );
			ret.setInfo( msg );
			logger.error( "hubSpotProductsUsingBatch " + action + ":", e );
		}
		catch ( final IOException e )
		{
			final var format = "json[%s], csvline[%s]";
			final var msg = String.format( format, json, Arrays.toString( csvLine ) );
			ret.setResultsMessage( e.getMessage() );
			ret.setInfo( msg );
			logger.error( "hubSpotProductsUsingBatch " + action + ":", e );
		}
		catch ( final ParseException e )
		{
			final var format = "json[%s], csvline[%s]";
			final var msg = String.format( format, json, Arrays.toString( csvLine ) );
			ret.setResultsMessage( e.getMessage() );
			ret.setInfo( msg );
			logger.error( "hubSpotProductsUsingBatch " + action + ":", e );
		}

		ret.setReturnValue( totalProductsUpdated );
		return ret;
	}

}
