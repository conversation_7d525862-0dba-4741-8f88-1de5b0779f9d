/* Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this
 * license Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Main.java to edit this template */
package com.kaplanco.hubspot.products.imp;

import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import javax.sql.DataSource;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.opencsv.CSVParser;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.exceptions.CsvValidationException;
import com.zaxxer.hikari.HikariDataSource;

/**
 * <AUTHOR>
 */
public class UpdateProducts
{
	protected Logger logger = LogManager.getFormatterLogger( UpdateProducts.class );
	public UpdateProducts()
	{

	}

	protected class CreateImportRecordResult
	{
		final public static String SUCCESS = "success";
		final private Long id;
		final private String result;

		public CreateImportRecordResult( final Long _id, final String _result )
		{
			id = _id;
			result = _result;
		}

		public Long getId()
		{
			return id;
		}

		public String getResult()
		{
			return result;
		}

		Boolean wasSuccessful()
		{
			Boolean ret = false;
			if ( result != null && CreateImportRecordResult.SUCCESS.equals( result ) )
			{
				ret = true;
			}

			return ret;
		}
	}

	/**
	 * 
	 * @param conn
	 * @param importSource
	 * @param ImportDataType
	 * @return
	 */
	protected CreateImportRecordResult createImportRecord(	final Connection conn,
																				final String importSource,
																				final String ImportDataType )
	{
		Long import_id = null;
		String results = "";

		String sql = "insert into imports(import_source,import_data_type,date_imported,import_status,import_start_date)";
		sql += " values(?,?,?,?,?)";
		sql += " returning id";
		PreparedStatement statement;
		try
		{
			statement = conn.prepareStatement( sql );
			statement.setString( 1, importSource );
			statement.setString( 2, ImportDataType );

			final Timestamp ts = new Timestamp( System.currentTimeMillis() );
			statement.setTimestamp( 3, ts );
			statement.setString( 4, "Processing" );
			statement.setTimestamp( 5, ts );

			final ResultSet rs = statement.executeQuery();
			rs.next();
			import_id = rs.getLong( 1 );
			results = CreateImportRecordResult.SUCCESS;
		}
		catch ( final SQLException e )
		{
			results = e.getMessage();
			e.printStackTrace();
		}

		final CreateImportRecordResult ret = new CreateImportRecordResult( import_id, results );
		return ret;
	}

	protected static DataSource createDataSource()
	{
		final HikariDataSource ds = new HikariDataSource();
		ds.setJdbcUrl( "********************************************" );
		ds.setUsername( "postgres" );
		ds.setPassword( "elmerMTW123" );
		return ds;
	}

	protected void processData( final CSVReader csvReader )
	{
		final DataSource dataSource = createDataSource();

		try ( Connection conn = dataSource.getConnection() )
		{
			final boolean isValid = conn.isValid( 0 );
			if ( isValid )
			{
				final CreateImportRecordResult result = createImportRecord( conn, "ERP", "Products" );
				if ( result.wasSuccessful() )
				{
					// Read CSV line by line and use the string array as you want
					String[] nextLine;

					int cnt = 0;
					while ( ( nextLine = csvReader.readNext() ) != null && cnt < 10 )
					{
						if ( nextLine != null )
						{
							// Verifying the read data here
							// System.out.println( Arrays.toString( nextLine ) );
							logger.trace( Arrays.toString( nextLine ) );
						}

						cnt++ ;
					}
				}
			}
		}
		catch ( final SQLException se )
		{
			se.printStackTrace();
		}
		catch ( final CsvValidationException e )
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		catch ( final IOException e )
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void processCsv( final String file )
	{
		try
		{
			logger.info( "Processing file:" + file );

			// Create an object of filereader
			// class with CSV file as a parameter.
			final FileReader filereader = new FileReader( file );

			// Build reader instance
			// Read data.csv
			// Default separator is pipe
			final CSVParser csvParser = new CSVParserBuilder()
				.withSeparator( '|' )
				.withIgnoreQuotations( true )
				.build();

			// create csvReader object passing
			// file reader as a parameter
			// Start reading from line number 2 (line numbers start from zero)
			final CSVReader csvReader = new CSVReaderBuilder( filereader )
				.withSkipLines( 1 )
				.withCSVParser( csvParser )
				.build();

			processData( csvReader );

			csvReader.close();
		}
		catch ( final Exception e )
		{
			e.printStackTrace();
		}

	}

	/**
	 * @param args the command line arguments
	 */
	public static void main( final String[] args )
	{
		final Logger logger = LogManager.getRootLogger();
		logger
			.debug( "Configuration File Defined To Be :: "
				+ System.getProperty( "log4j.configurationFile" ) );

		final UpdateProducts up = new UpdateProducts();

		final String file = "C:\\Users\\<USER>\\Downloads\\imports\\hubspot-product-list.csv";
		up.processCsv( file );
	}

}
