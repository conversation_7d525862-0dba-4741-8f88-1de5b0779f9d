package com.kaplanco.hubspot.products;

public class GhProductEntry
{
	private Long jobId;
	private String bookId;
	private String isbn;
	private String title;
	private String url;
	private String description;
	private String retailPrice;
	private String cost;
	private String shipping;
	private String weight;
	private String subtitle;
	private String thumbnailUrl;
	private String detailImageUrl;
	private String detailImage;
	private String discountable;
	private String productAvailability;

	public Long getJobId()
	{
		return jobId;
	}

	public void setJobId( Long jobId )
	{
		this.jobId = jobId;
	}

	public String getBookId()
	{
		return bookId;
	}

	public void setBookId( String bookId )
	{
		this.bookId = bookId;
	}

	public String getIsbn()
	{
		return isbn;
	}

	public void setIsbn( String isbn )
	{
		this.isbn = isbn;
	}

	public String getTitle()
	{
		return title;
	}

	public void setTitle( String title )
	{
		this.title = title;
	}

	public String getUrl()
	{
		return url;
	}

	public void setUrl( String url )
	{
		this.url = url;
	}

	public String getDescription()
	{
		return description;
	}

	public void setDescription( String description )
	{
		this.description = description;
	}

	public String getRetailPrice()
	{
		return retailPrice;
	}

	public void setRetailPrice( String retailPrice )
	{
		this.retailPrice = retailPrice;
	}

	public String getCost()
	{
		return cost;
	}

	public void setCost( String cost )
	{
		this.cost = cost;
	}

	public String getShipping()
	{
		return shipping;
	}

	public void setShipping( String shipping )
	{
		this.shipping = shipping;
	}

	public String getWeight()
	{
		return weight;
	}

	public void setWeight( String weight )
	{
		this.weight = weight;
	}

	public String getSubtitle()
	{
		return subtitle;
	}

	public void setSubtitle( String subtitle )
	{
		this.subtitle = subtitle;
	}

	public String getThumbnailUrl()
	{
		return thumbnailUrl;
	}

	public void setThumbnailUrl( String thumbnailUrl )
	{
		this.thumbnailUrl = thumbnailUrl;
	}

	public String getDetailImageUrl()
	{
		return detailImageUrl;
	}

	public void setDetailImageUrl( String detailImageUrl )
	{
		this.detailImageUrl = detailImageUrl;
	}

	public String getDetailImage()
	{
		return detailImage;
	}

	public void setDetailImage( String detailImage )
	{
		this.detailImage = detailImage;
	}

	public String getDiscountable()
	{
		return discountable;
	}

	public void setDiscountable( String discountable )
	{
		this.discountable = discountable;
	}

	public String getProductAvailability()
	{
		return productAvailability;
	}

	public void setProductAvailability( String productAvailability )
	{
		this.productAvailability = productAvailability;
	}

}
