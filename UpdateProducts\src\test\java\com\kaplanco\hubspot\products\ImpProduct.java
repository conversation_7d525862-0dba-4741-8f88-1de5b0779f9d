package com.kaplanco.hubspot.products;

public class ImpProduct
{
	private Long job_id;
	private String product_id;
	private String productName;
	private String product_image;
	private String product_image_url;
	private String product_url;
	private String description;
	private String truck_sw;
	private String drop_ship_sw;
	private String retail_price;
	private String qty_on_hand;
	private String discountable_sw;
	private String kaplan_exclusive;
	private String nla;
	private String to_be_nla;
	private String second_day_up_charge;
	private String next_day_up_charge;
	private String ps_add_date;
	private String is_kaplan_product;
	private String kaplan_product_id;
	private final String product_action;

	public ImpProduct(	Long _job_id,
								String _product_id,
								String _productName,
								String _product_image,
								String _product_image_url,
								String _product_url,
								String _description,
								String _truck_sw,
								String _drop_ship_sw,
								String _retail_price,
								String _qty_on_hand,
								String _discountable_sw,
								String _kaplan_exclusive,
								String _nla,
								String _to_be_nla,
								String _second_day_up_charge,
								String _next_day_up_charge,
								String _ps_add_date,
								String _is_kaplan_product,
								String _kaplan_product_id,
								String _product_action )
	{
		job_id = _job_id;
		product_id = _product_id;
		productName = _productName;
		product_image = _product_image;
		product_image_url = _product_image_url;
		product_url = _product_url;
		description = _description;
		truck_sw = _truck_sw;
		drop_ship_sw = _drop_ship_sw;
		retail_price = _retail_price;
		qty_on_hand = _qty_on_hand;
		discountable_sw = _discountable_sw;
		kaplan_exclusive = _kaplan_exclusive;
		nla = _nla;
		to_be_nla = _to_be_nla;
		second_day_up_charge = _second_day_up_charge;
		next_day_up_charge = _next_day_up_charge;
		ps_add_date = _ps_add_date;
		is_kaplan_product = _is_kaplan_product;
		kaplan_product_id = _kaplan_product_id;
		product_action = _product_action;
	}

	public Long getJob_id()
	{
		return job_id;
	}

	public void setJob_id( Long job_id )
	{
		this.job_id = job_id;
	}

	public String getProduct_id()
	{
		return product_id;
	}

	public void setProduct_id( String product_id )
	{
		this.product_id = product_id;
	}

	public String getProductName()
	{
		return productName;
	}

	public void setProductName( String productName )
	{
		this.productName = productName;
	}

	public String getProduct_image()
	{
		return product_image;
	}

	public void setProduct_image( String product_image )
	{
		this.product_image = product_image;
	}

	public String getProduct_image_url()
	{
		return product_image_url;
	}

	public void setProduct_image_url( String product_image_url )
	{
		this.product_image_url = product_image_url;
	}

	public String getProduct_url()
	{
		return product_url;
	}

	public void setProduct_url( String product_url )
	{
		this.product_url = product_url;
	}

	public String getDescription()
	{
		return description;
	}

	public void setDescription( String description )
	{
		this.description = description;
	}

	public String getTruck_sw()
	{
		return truck_sw;
	}

	public void setTruck_sw( String truck_sw )
	{
		this.truck_sw = truck_sw;
	}

	public String getDrop_ship_sw()
	{
		return drop_ship_sw;
	}

	public void setDrop_ship_sw( String drop_ship_sw )
	{
		this.drop_ship_sw = drop_ship_sw;
	}

	public String getRetail_price()
	{
		return retail_price;
	}

	public void setRetail_price( String retail_price )
	{
		this.retail_price = retail_price;
	}

	public String getQty_on_hand()
	{
		return qty_on_hand;
	}

	public void setQty_on_hand( String qty_on_hand )
	{
		this.qty_on_hand = qty_on_hand;
	}

	public String getDiscountable_sw()
	{
		return discountable_sw;
	}

	public void setDiscountable_sw( String discountable_sw )
	{
		this.discountable_sw = discountable_sw;
	}

	public String getKaplan_exclusive()
	{
		return kaplan_exclusive;
	}

	public void setKaplan_exclusive( String kaplan_exclusive )
	{
		this.kaplan_exclusive = kaplan_exclusive;
	}

	public String getNla()
	{
		return nla;
	}

	public void setNla( String nla )
	{
		this.nla = nla;
	}

	public String getTo_be_nla()
	{
		return to_be_nla;
	}

	public void setTo_be_nla( String to_be_nla )
	{
		this.to_be_nla = to_be_nla;
	}

	public String getSecond_day_up_charge()
	{
		return second_day_up_charge;
	}

	public void setSecond_day_up_charge( String second_day_up_charge )
	{
		this.second_day_up_charge = second_day_up_charge;
	}

	public String getNext_day_up_charge()
	{
		return next_day_up_charge;
	}

	public void setNext_day_up_charge( String next_day_up_charge )
	{
		this.next_day_up_charge = next_day_up_charge;
	}

	public String getPs_add_date()
	{
		return ps_add_date;
	}

	public void setPs_add_date( String ps_add_date )
	{
		this.ps_add_date = ps_add_date;
	}

	public String getIs_kaplan_product()
	{
		return is_kaplan_product;
	}

	public void setIs_kaplan_product( String is_kaplan_product )
	{
		this.is_kaplan_product = is_kaplan_product;
	}

	public String getKaplan_product_id()
	{
		return kaplan_product_id;
	}

	public void setKaplan_product_id( String kaplan_product_id )
	{
		this.kaplan_product_id = kaplan_product_id;
	}

	public String getProduct_action()
	{
		return product_action;
	}

}
