-- DROP FUNCTION products.kelc_to_gh_lookup(int8);

CREATE OR REPLACE FUNCTION products.kelc_to_gh_lookup(jobid bigint)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
	BEGIN
		UPDATE hubspot_products AS gh
		SET description = kelc.description,
			product_name = kelc.product_name,
			sale_price = kelc.sale_price,
			retail_price = kelc.retail_price,
			nla = kelc.nla,
			to_be_nla = kelc.to_be_nla,
			product_cost = kelc.product_cost,
			isbn = kelc.isbn,
			job_id = kelc.job_id,
			product_action = kelc.product_action
		FROM gh_to_kaplan_product_id_lookup AS lkup
		INNER JOIN hubspot_products AS kelc
			ON kelc.product_id = lkup.kaplan_product_no 
		WHERE gh.is_kaplan_product = 'N'
			AND kelc.is_kaplan_product  = 'Y'
			AND lkup.gh_product_no = gh.product_id
			AND kelc.job_id = jobid;
		
		RETURN 1;
	END;
$function$
;
