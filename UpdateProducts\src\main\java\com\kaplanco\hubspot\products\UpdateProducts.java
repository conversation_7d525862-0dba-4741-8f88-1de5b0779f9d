package com.kaplanco.hubspot.products;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.TreeSet;
import javax.sql.DataSource;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.kaplanco.hubspot.MethodReturn;
import com.kaplanco.hubspot.exports.HubSpotExportMethods;
import com.kaplanco.hubspot.imports.HubSpotImportMethods;
import com.kaplanco.hubspot.products.imports.BatchProcessingProducts;
import com.opencsv.CSVParser;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.CSVWriter;
import com.opencsv.exceptions.CsvValidationException;
import com.zaxxer.hikari.HikariDataSource;
import net.schmizz.sshj.SSHClient;
import net.schmizz.sshj.sftp.SFTPClient;
import net.schmizz.sshj.transport.verification.PromiscuousVerifier;

public class UpdateProducts
{
	protected Logger logger = LogManager.getFormatterLogger( UpdateProducts.class );
	private Properties properties;
	private ProductParameters parameters;
	private final String localFolder;
	private final HubSpotImportMethods hsim;
	private final HubSpotExportMethods hsem;

	public static final String HUBSPOT_IMPORT_STATUS_DONE = "DONE";

	public static final String HUBSPOT_IMPORT_ACTION_CREATE = "CREATED";
	public static final String HUBSPOT_IMPORT_ACTION_UPDATE = "UPDATE";

	public static final String HUBSPOT_KELC_CREATE_FILE_BASE = "_KELC_CreatedProductsList.csv";
	public static final String HUBSPOT_GH_CREATE_FILE_BASE = "_GH_CreatedProductsList.csv";

	public static final String HUBSPOT_KELC_UPDATE_FILE_BASE = "_KELC_UpdatedProductsList.csv";
	public static final String HUBSPOT_GH_UPDATE_FILE_BASE = "_GH_UpdatedProductsList.csv";

	public static final Integer HUBSPOT_ORDER_DESTINATIONS_PER_BATCH_CREATE = 100;

	public static final String ADPEARANCE_HUB_PRODUCT_LIST = "hubspot-product-list.csv";

	public static final String[] HUBSPOT_IMPORT_CSV_PRODUCTS_HEADERS = {
		"Name",
		"SKU",
		"description",
		"product_image_url",
		"product_url",
		"DATE",
		"drop_ship_sw",
		"kaplan_exclusive",
		"second_day_up_charge",
		"next_day_up_charge",
		"nla",
		"to_be_nla",
		"truck_sw",
		"retail_price",
		"sale_price",
		"discountable_sw",
		"product_action",
		"job_id",
		"hubspot_id",
		"business_unit" };

	public UpdateProducts( final String configFileName )
	{
		loadProperties( configFileName );

		localFolder = properties.getProperty( "LOCAL_FOLDER" );
		final var kelcProductsApiKey = properties.getProperty( "KELC_PRODUCTS_API_KEY" );
		hsim = new HubSpotImportMethods( kelcProductsApiKey );
		hsem = new HubSpotExportMethods( kelcProductsApiKey );
	}

	public void loadProperties( final String configFileName )
	{
		properties = new Properties();
		try
		{
			// final var resource = UpdateProducts.class.getResourceAsStream( configFileName );
			final var file = new File( configFileName );
			final var resource = new FileInputStream( file );

			properties.load( resource );
		}
		catch ( final IOException e )
		{
			e.printStackTrace();
			return;
		}
	}

	public String getProperty( final String propertyName )
	{
		return properties.getProperty( propertyName );
	}

	public void logJobMessage( final ProductParameters params, final Map< String, String > properties, final String message )
	{
		try
		{
			final PreparedStatement statement;

			if ( params.isResultSetActive() )
			{
				if ( params.isResultSetAfterLast() )
				{
					final var sql = "insert into product_logs(job_id, message, created_at) values (?,?,now())";
					statement = params.getConnection().prepareStatement( sql );
					statement.setLong( 1, params.getJobId() );

					statement.setString( 2, message );

					params.startTimeDatabase();
					statement.execute();
					params.stopTimeDatabase();
				}
				else
				{
					var sql = "INSERT INTO product_logs (job_id, product_id, product_name, product_image, product_image_url, product_url,";
					sql += " description, truck_sw, drop_ship_sw, retail_price, sale_price, qty_on_hand,";
					sql += " discountable_sw, kaplan_exclusive, nla, to_be_nla, second_day_up_charge,";
					sql += " next_day_up_charge, in_stock_date, ps_add_date, is_kaplan_product,";
					sql += " kaplan_product_id, created_at, message)";
					sql += " VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,NOW(),?)";
					statement = params.getConnection().prepareStatement( sql );

					statement.setLong( 1, params.getJobId() );

					final var product_id = properties.get( "product_id" );
					statement.setString( 2, product_id );

					final var product_name = properties.get( "product_name" );
					statement.setString( 3, product_name );

					final var product_image = properties.get( "product_image" );
					statement.setString( 4, product_image );

					final var product_image_url = properties.get( "product_image_url" );
					statement.setString( 5, product_image_url );

					final var product_url = properties.get( "product_url" );
					statement.setString( 6, product_url );

					final var description = properties.get( "description" );
					statement.setString( 7, description );

					final var truck_sw = properties.get( "truck_sw" );
					statement.setString( 8, truck_sw );

					final var drop_ship_sw = properties.get( "drop_ship_sw" );
					statement.setString( 9, drop_ship_sw );

					final var retail_price = properties.get( "retail_price" );
					statement.setString( 10, retail_price );

					final var sale_price = properties.get( "sale_price" );
					statement.setString( 11, sale_price );

					final var qty_on_hand = properties.get( "qty_on_hand" );
					statement.setString( 12, qty_on_hand );

					final var discountable_sw = properties.get( "discountable_sw" );
					statement.setString( 13, discountable_sw );

					final var kaplan_exclusive = properties.get( "kaplan_exclusive" );
					statement.setString( 14, kaplan_exclusive );

					final var nla = properties.get( "nla" );
					statement.setString( 15, nla );

					final var to_be_nla = properties.get( "to_be_nla" );
					statement.setString( 16, to_be_nla );

					final var second_day_up_charge = properties.get( "second_day_up_charge" );
					statement.setString( 17, second_day_up_charge );

					final var next_day_up_charge = properties.get( "next_day_up_charge" );
					statement.setString( 18, next_day_up_charge );

					final var in_stock_date = properties.get( "in_stock_date" );
					statement.setString( 19, in_stock_date );

					final var ps_add_date = properties.get( "ps_add_date" );
					statement.setString( 20, ps_add_date );

					final var is_kaplan_product = properties.get( "is_kaplan_product" );
					statement.setString( 21, is_kaplan_product );

					final var kaplan_product_id = properties.get( "kaplan_product_id" );
					statement.setString( 22, kaplan_product_id );

					statement.setString( 23, message );

					params.startTimeDatabase();
					statement.execute();
					params.stopTimeDatabase();
				}
			}
			else
			{
				Connection conn = null;
				if ( params != null )
				{
					conn = params.getConnection();
				}

				if ( conn != null )
				{
					var sql = "insert into product_logs(job_id, message, created_at)";
					sql += " values(?,?,now())";

					try ( var stmnt = params.getConnection().prepareStatement( sql ) )
					{
						stmnt.setLong( 1, params.getJobId() );
						stmnt.setString( 2, message );

						params.startTimeDatabase();
						stmnt.execute();
						params.stopTimeDatabase();
					}
				}
				else
				{
					logger.error( message );
				}
			}
		}
		catch ( final SQLException e )
		{
			e.printStackTrace();
		}

	}

	public void logJobMessage( final ProductParameters params, final String message )
	{
		try
		{
			final var properties = new HashMap< String, String >();
			if ( params.isResultSetActive() )
			{
				if ( !params.isResultSetAfterLast() )
				{
					properties.put( "product_id", params.getColumnStringValue( "product_id" ) );
					properties.put( "product_name", params.getColumnStringValue( "product_name" ) );
					properties.put( "product_image", params.getColumnStringValue( "product_image" ) );
					properties.put( "product_image_url", params.getColumnStringValue( "product_image_url" ) );
					properties.put( "product_url", params.getColumnStringValue( "product_url" ) );
					properties.put( "description", params.getColumnStringValue( "description" ) );
					properties.put( "truck_sw", params.getColumnStringValue( "truck_sw" ) );
					properties.put( "drop_ship_sw", params.getColumnStringValue( "drop_ship_sw" ) );
					properties.put( "retail_price", params.getColumnStringValue( "retail_price" ) );
					properties.put( "sale_price", params.getColumnStringValue( "sale_price" ) );
					properties.put( "qty_on_hand", params.getColumnStringValue( "qty_on_hand" ) );
					properties.put( "discountable_sw", params.getColumnStringValue( "discountable_sw" ) );
					properties.put( "kaplan_exclusive", params.getColumnStringValue( "kaplan_exclusive" ) );
					properties.put( "nla", params.getColumnStringValue( "nla" ) );
					properties.put( "to_be_nla", params.getColumnStringValue( "to_be_nla" ) );
					properties.put( "second_day_up_charge", params.getColumnStringValue( "second_day_up_charge" ) );
					properties.put( "next_day_up_charge", params.getColumnStringValue( "next_day_up_charge" ) );
					properties.put( "in_stock_date", params.getColumnStringValue( "in_stock_date" ) );
					properties.put( "ps_add_date", params.getColumnStringValue( "ps_add_date" ) );
					properties.put( "is_kaplan_product", params.getColumnStringValue( "is_kaplan_product" ) );
					properties.put( "kaplan_product_id", params.getColumnStringValue( "kaplan_product_id" ) );
					logJobMessage( params, properties, message );
				}
				else
				{
					logJobMessage( params, properties, message );
				}
			}
			else
			{
				logJobMessage( params, properties, message );
			}
		}
		catch ( final SQLException e )
		{
			e.printStackTrace();
		}

	}

	public MethodReturn< String > copyFileFromSftpServer( final ProductParameters params,
																			final String filename,
																			final String localFilePath )
	{
		final var ret = new MethodReturn< String >();

		final var client = new SSHClient();
		client.addHostKeyVerifier( new PromiscuousVerifier() );
		try
		{
			params.startTimeSftp();
			client.connect( properties.getProperty( "SFTP_HOST" ) );
			client.authPassword( properties.getProperty( "SFTP_USERNAME" ), properties.getProperty( "SFTP_PASSWORD" ) );
			params.stopTimesFtp();

			var destFilePath = localFilePath;
			if ( !localFilePath.endsWith( "/" ) )
			{
				destFilePath += "/";
			}
			destFilePath += filename;

			params.startTimeSftp();
			final var sftpClient = client.newSFTPClient();
			sftpClient.get( properties.getProperty( "SFTP_REMOTE_FOLDER" ) + filename, destFilePath );
			params.stopTimesFtp();

			sftpClient.close();
			client.disconnect();
			client.close();
		}
		catch ( final IOException e )
		{
			ret.setResultsMessage( "copyFileFromSftpServer:" + e.getMessage() );
			logJobMessage( params, "copyFileFromSftpServer:" + e.getMessage() );

			e.printStackTrace();
		}

		return ret;
	}

	public DataSource createDataSource()
	{
		final var ds = new HikariDataSource();

		ds.setJdbcUrl( properties.getProperty( "POSTGRESQL_DATABASE_CONNECTION_STRING" ) );
		ds.setUsername( properties.getProperty( "DATABASE_USERNAME" ) );
		ds.setPassword( properties.getProperty( "DATABASE_PASSWORD" ) );

		return ds;
	}

	protected MethodReturn< String > moveSftpFileToArchive(	final ProductParameters params,
																				final String filename,
																				final String localFilePath )
	{
		final var ret = new MethodReturn< String >();
		final var client = new SSHClient();
		client.addHostKeyVerifier( new PromiscuousVerifier() );
		try
		{
			params.startTimeSftp();
			client.connect( properties.getProperty( "SFTP_HOST" ) );
			client.authPassword( properties.getProperty( "SFTP_USERNAME" ), properties.getProperty( "SFTP_PASSWORD" ) );
			params.stopTimesFtp();

			final var date = Calendar.getInstance().getTime();
			final DateFormat dateFormat = new SimpleDateFormat( "yyyy-MM-dd-hh-mm-ss" );
			final var strDate = dateFormat.format( date );

			final var sftpClient = client.newSFTPClient();

			var destFilePath = localFilePath;
			if ( !localFilePath.endsWith( "/" ) )
			{
				destFilePath += "/";
			}
			destFilePath += filename;
			var sourceFilename = destFilePath;
			final String targetDestination = properties.getProperty( "SFTP_REMOTE_FOLDER" ) + "Archive/";

			var targetFilename = targetDestination;
			if ( filename == UpdateProducts.ADPEARANCE_HUB_PRODUCT_LIST )
			{
				targetFilename = targetFilename + params.getJobId() + '_';
			}
			targetFilename = targetFilename + filename + "-" + strDate;
			sourceFilename = localFilePath + filename;

			params.startTimeSftp();
			sftpClient.getFileTransfer().setPreserveAttributes( false );
			sftpClient.put( sourceFilename, targetFilename );
			params.stopTimesFtp();

			sftpClient.close();
			client.disconnect();
			client.close();
		}
		catch ( final IOException e )
		{
			ret.setResultsMessage( "moveSftpFileToArchive:" + e.getMessage() );
			logJobMessage( params, "moveSftpFileToArchive:" + e.getMessage() );

			e.printStackTrace();
		}

		return ret;
	}

	private MethodReturn< String > updateJobsStatus( final ProductParameters params, final String jobStatus )
	{
		final var ret = new MethodReturn< String >();
		ret.setResultsMessage( "" );

		var sql = "update jobs set status = '";
		sql += jobStatus + "', hubspot_api_count = ";
		sql += params.getHubSpotApiCount().toString();
		sql += " where id = ";
		sql += params.getJobId().toString();

		try ( var updateStatement = params.getConnection().createStatement() )
		{
			updateStatement.executeUpdate( sql );
		}
		catch ( final SQLException e )
		{
			final var errorCode = String.valueOf( e.getErrorCode() );
			ret.setErrorCode( errorCode );
			ret.setResultsMessage( e.getMessage() );

			logJobMessage( params, "updateJobsStatus-" + jobStatus + ":" + e.getMessage() );
			e.printStackTrace();
		}

		return ret;
	}

	public MethodReturn< Integer > updateHubSpotProductsUsingBatch(	final ProductParameters params,
																							final String filename,
																							final String filePath )
	{
		var ret = new MethodReturn< Integer >();

		try
		{
			final String fileNameWithPath = filePath + filename;
			params.setCsvFileName( fileNameWithPath );
			final var insertsFilePath = params.getCsvFileName();

			// Create an object of filereader
			// class with CSV file as a parameter.
			final var filereader = new FileReader( insertsFilePath );

			// Build reader instance
			// Read data.csv
			// Default separator is pipe
			final var csvParser = new CSVParserBuilder()
				.withSeparator( '|' )
				.withQuoteChar( '"' )
				.withIgnoreQuotations( false )
				.build();

			// create csvReader object passing
			// file reader as a parameter
			// Start reading from line number 1 (no headers for Create/Update CSVs)
			final var csvReader = new CSVReaderBuilder( filereader ).withSkipLines( 0 ).withCSVParser( csvParser ).build();

			final var bpd = new BatchProcessingProducts( params, this, UpdateProducts.HUBSPOT_IMPORT_ACTION_UPDATE );
			final Boolean isKaplanProduct = filename.equals( params.getJobId() + UpdateProducts.HUBSPOT_KELC_CREATE_FILE_BASE )
				|| filename.equals( params.getJobId() + UpdateProducts.HUBSPOT_KELC_UPDATE_FILE_BASE ); // Or
																																		// condition
																																		// added
																																		// 07Mar25.
																																		// CRC

			ret = bpd.hubSpotProductsUsingBatch( csvReader, isKaplanProduct );
			final var errorMessage = ret.getResultsMessage();
			if ( errorMessage == null || errorMessage.isBlank() )
			{
				var createdProducts = ret.getReturnValue();
				if ( createdProducts == null )
				{
					createdProducts = 0;
				}
				logJobMessage( params, "Updated " + createdProducts.toString() + " Products" );
			}
			else
			{
				logJobMessage( params, "Error Updating Products by Batch:" + errorMessage );
			}

			// Close file so it can be deleted
			csvReader.close();
			filereader.close();
		}
		catch ( final IOException e )
		{
			e.printStackTrace();
			logJobMessage( params, "updateHubSpotProductsUsingBatch:" + e.getMessage() );
		}
		catch ( final InterruptedException e )
		{
			e.printStackTrace();
			logJobMessage( params, "updateHubSpotProductsUsingBatch:" + e.getMessage() );
		}

		return ret;
	}

	public MethodReturn< Integer > createHubSpotProductsUsingBatch(	final ProductParameters params,
																							final String filename,
																							final String filePath ) throws InterruptedException
	{
		var ret = new MethodReturn< Integer >();

		try
		{
			final String fileNameWithPath = filePath + filename;
			params.setCsvFileName( fileNameWithPath );
			final var insertsFilePath = params.getCsvFileName();

			// Create an object of filereader
			// class with CSV file as a parameter.
			final var filereader = new FileReader( insertsFilePath );

			// Build reader instance
			// Read data.csv
			// Default separator is pipe
			final var csvParser = new CSVParserBuilder()
				.withSeparator( '|' )
				.withQuoteChar( '"' )
				.withIgnoreQuotations( false )
				.build();

			// create csvReader object passing
			// file reader as a parameter
			final var csvReader = new CSVReaderBuilder( filereader ).withCSVParser( csvParser ).build();

			final var bpd = new BatchProcessingProducts( params, this, UpdateProducts.HUBSPOT_IMPORT_ACTION_CREATE );

			final Boolean isKaplanProduct = filename.equals( params.getJobId() + UpdateProducts.HUBSPOT_KELC_CREATE_FILE_BASE );

			ret = bpd.hubSpotProductsUsingBatch( csvReader, isKaplanProduct );

			final var errorMessage = ret.getResultsMessage();
			if ( errorMessage != null && !errorMessage.isBlank() )
			{
				logJobMessage( params, "createHubSpotProductsUsingBatch:" + errorMessage );
			}

			// Close file so it can be deleted
			csvReader.close();
			filereader.close();
		}
		catch ( final IOException e )
		{
			logger.error( "createHubSpotProductsUsingBatch", e );
		}

		return ret;
	}

	protected MethodReturn< String > processProductsData( final ProductParameters params,
																			final String filename,
																			final String filePath ) throws InterruptedException
	{
		final var ret = new MethodReturn< String >();

		final var mrs = updateJobsStatus( params, "Processing Products data" );
		if ( mrs.getResultsMessage().isBlank() )
		{
			try
			{
				MethodReturn< Integer > mri = null;

				if ( filename.equals( params.getJobId() + UpdateProducts.HUBSPOT_KELC_UPDATE_FILE_BASE )
					|| filename.equals( params.getJobId() + UpdateProducts.HUBSPOT_GH_UPDATE_FILE_BASE ) )
				{
					mri = updateHubSpotProductsUsingBatch( params, filename, filePath );
				}
				else if ( filename.equals( params.getJobId() + UpdateProducts.HUBSPOT_KELC_CREATE_FILE_BASE )
					|| filename.equals( params.getJobId() + UpdateProducts.HUBSPOT_GH_CREATE_FILE_BASE ) )
				{
					mri = createHubSpotProductsUsingBatch( params, filename, filePath );
					final var errorMessage = mri.getResultsMessage();
					if ( errorMessage == null || errorMessage.isBlank() )
					{
						var productsCreated = mri.getReturnValue();
						if ( productsCreated == null )
						{
							productsCreated = 0;
						}

						logJobMessage( params, productsCreated.toString() + " Products created" );
					}
					else
					{
						logJobMessage( params, "Error creating products:" + errorMessage );
					}
				}

				if ( mri != null && mri.getResultsMessage() != null && !mri.getResultsMessage().isBlank() )
				{
					ret.setErrorCode( mri.getErrorCode() );
					ret.setResultsMessage( mri.getResultsMessage() );
				}
			}
			catch ( final Exception e )
			{
				ret.setResultsMessage( e.getMessage() );
				logJobMessage( params, "processProductsData:" + e.getMessage() );

				e.printStackTrace();
			}
		}

		moveSftpFileToArchive( params, filename, filePath );

		return ret;
	}

	public MethodReturn< String > processImportedCsvData( final ProductParameters params,
																			final String localFolder,
																			final String filename ) throws InterruptedException
	{
		final String errorMessage = null;
		final String errorCode = null;

		var ret = new MethodReturn< String >();

		final var jobId = params.getJobId();
		if ( jobId > 0 )
		{
			// This is where all of the records in the CSV get processed
			try
			{
				// params.setCsvFileName( "HubSpotErpImportDealsFile.csv" );
				ret = processProductsData( params, filename, localFolder );

			}
			catch ( final Exception e )
			{
				ret.setResultsMessage( e.getMessage() );
				e.printStackTrace();
			}
		}

		ret.setResultsMessage( errorMessage );
		ret.setErrorCode( errorCode );

		return ret;
	}

	public MethodReturn< Long > importProducts(	final ProductParameters params,
																final String filename,
																final String localFolder,
																final Long jobId )
	{
		final var ret = new MethodReturn< Long >();
		String errorMessage;
		String errorCode = null;
		try
		{
			logger.info( "Processing file:" + localFolder + filename );

			final var dataSource = createDataSource();

			try ( var conn = dataSource.getConnection() )
			{
				params.setConnection( conn );

				params.setJobId( jobId );

				final var mrhsir = processImportedCsvData( params, localFolder, filename );

				var sql = "update jobs set status = 'Finished', finished = now() where id = ";
				sql += params.getJobId().toString();

				try ( final var updateStatment = params.getConnection().createStatement() )
				{
					params.startTimeDatabase();
					updateStatment.executeUpdate( sql );
					params.stopTimeDatabase();
				}
				catch ( final SQLException e )
				{
					System.out.println( "Error trying to update finish time for job:" + e.getMessage() );
					e.printStackTrace();
				}

				conn.close();
			}
			catch ( final SQLException se )
			{
				errorMessage = se.getMessage();
				errorCode = String.valueOf( se.getErrorCode() );
				logJobMessage( params, "main[ErrorCode:" + errorCode + ":" + errorMessage + "]" );
				se.printStackTrace();
			}

		}
		catch ( final Exception e )
		{
			errorMessage = e.getMessage();
			logJobMessage( params, "main[" + errorMessage + "]" );
			e.printStackTrace();
		}

		logger.info( "Finished Processing file:" + localFolder + filename );

		return ret;
	}

	public MethodReturn< Long > importSourceCSV( final ProductParameters params, final String localFolder, final Long jobId )
	{
		final var ret = new MethodReturn< Long >();
		String errorMessage;
		String errorCode = null;
		final String AllProductsfilename = UpdateProducts.ADPEARANCE_HUB_PRODUCT_LIST;
		try
		{
			logger.info( "Processing file:" + localFolder + AllProductsfilename );

			final var dataSource = createDataSource();

			try ( var conn = dataSource.getConnection() )
			{
				params.setConnection( conn );

				params.setJobId( jobId );

				moveSftpFileToArchive( params, AllProductsfilename, localFolder );

				conn.close();
			}
			catch ( final SQLException se )
			{
				errorMessage = se.getMessage();
				errorCode = String.valueOf( se.getErrorCode() );
				logJobMessage( params, "main[ErrorCode:" + errorCode + ":" + errorMessage + "]" );
				se.printStackTrace();
			}

		}
		catch ( final Exception e )
		{
			errorMessage = e.getMessage();
			logJobMessage( params, "main[" + errorMessage + "]" );
			e.printStackTrace();
		}

		logger.info( "Finished Processing file:" + localFolder + AllProductsfilename );

		return ret;
	}

	private MethodReturn< String > processCsvFilesInSftpFolder( final ProductParameters params, final Long jobId )
	{
		final var ret = new MethodReturn< String >();

		// Sort by file name to make sure to process in order
		final var sortedFileList = new TreeSet< String >();
		// create list of file names
		sortedFileList.add( jobId + UpdateProducts.HUBSPOT_KELC_CREATE_FILE_BASE );
		sortedFileList.add( jobId + UpdateProducts.HUBSPOT_GH_CREATE_FILE_BASE );
		sortedFileList.add( jobId + UpdateProducts.HUBSPOT_KELC_UPDATE_FILE_BASE );
		sortedFileList.add( jobId + UpdateProducts.HUBSPOT_GH_UPDATE_FILE_BASE );

		for ( final String filename : sortedFileList )
		{
			importProducts( params, filename, localFolder, jobId );

			deleteLocalFile( filename );
		}
		// Added 08Apr25 to create archive of hubspot-product-list.csv file used. CRC

		importSourceCSV( params, localFolder, jobId );

		return ret;
	}

	private void deleteLocalFile( final String fileName )
	{
		final String filePathString = localFolder + fileName;
		final Path filePath = Paths.get( filePathString );

		if ( Files.isRegularFile( filePath ) )
		{
			try
			{
				Files.delete( filePath );
			}
			catch ( final IOException e )
			{
				e.printStackTrace();
			}
			/* final Boolean successfulDelete = localGenFil.delete(); if ( successfulDelete ) {
			 * System.out.println( "Successful deletion of " + genFile ); } else { System.out.println(
			 * "Failed deletion of " + genFile ); } */
		}
	}

	private void localCleanup()
	{
		logger.info( "Cleaning local files" );

		deleteLocalFile( UpdateProducts.ADPEARANCE_HUB_PRODUCT_LIST );
	}

	private Long startJob( final ProductParameters params )
	{
		String errorMessage;
		String errorCode = null;

		Long jobId = null;
		try
		{
			logger.info( "Starting job" );

			final var dataSource = createDataSource();

			try ( var conn = dataSource.getConnection() )
			{
				params.setConnection( conn );

				final var sql = "select products.create_job() as job_id;";

				try ( final var statment = params.getConnection().createStatement() )
				{
					params.startTimeDatabase();
					final ResultSet rs = statment.executeQuery( sql );
					if ( rs.next() )
					{
						jobId = rs.getLong( "job_id" );
						params.setJobId( jobId );
					}
					params.stopTimeDatabase();
				}
				catch ( final SQLException e )
				{
					System.out.println( "Error trying to start job:" + e.getMessage() );
					e.printStackTrace();
				}
			}
			catch ( final SQLException se )
			{
				errorMessage = se.getMessage();
				errorCode = String.valueOf( se.getErrorCode() );
				logJobMessage( params, "main[ErrorCode:" + errorCode + ":" + errorMessage + "]" );
				se.printStackTrace();
			}

		}
		catch ( final Exception e )
		{
			errorMessage = e.getMessage();
			logJobMessage( params, "main[" + errorMessage + "]" );
			e.printStackTrace();
		}

		return jobId;
	}

	private GhProductEntry createGhEnties( final Long jobId, final ResultSet ghProducts ) throws SQLException
	{
		final GhProductEntry product = new GhProductEntry();

		product.setJobId( jobId );

		final String bookId = ghProducts.getString( "product_book_id" );
		product.setBookId( bookId );

		final String isbn = ghProducts.getString( "product_isbn" );
		product.setIsbn( isbn );

		final String title = ghProducts.getString( "title" );
		product.setTitle( title );

		final String url = ghProducts.getString( "product_url" );
		product.setUrl( url );

		final String description = ghProducts.getString( "product_description1" );
		product.setDescription( description );

		final String retailPrice = ghProducts.getString( "product_retail_price" );
		product.setRetailPrice( retailPrice );

		final String cost = ghProducts.getString( "product_cost" );
		product.setCost( cost );

		final String shipping = ghProducts.getString( "product_shipping" );
		product.setShipping( shipping );

		final String weight = ghProducts.getString( "product_weight" );
		product.setWeight( weight );

		final String subtitle = ghProducts.getString( "product_subtitle" );
		product.setSubtitle( subtitle );

		final String thumbnailUrl = ghProducts.getString( "product_thumbnail_url" );
		product.setThumbnailUrl( thumbnailUrl );

		final String detailImageUrl = ghProducts.getString( "product_detail_image_url" );
		product.setDetailImageUrl( detailImageUrl );

		final String detailImage = ghProducts.getString( "product_detail_image" );
		product.setDetailImage( detailImage );

		final String discountable = ghProducts.getString( "product_discountable" );
		product.setDiscountable( discountable );

		final String availability = ghProducts.getString( "product_availability" );
		product.setProductAvailability( availability );

		return product;
	}

	private void handleHubspotProductCsvFile( final ProductParameters params, final Long jobId )
	{
		SFTPClient sftpClient = null;

		final SSHClient client = new SSHClient();
		client.addHostKeyVerifier( new PromiscuousVerifier() );
		try
		{
			client.connect( properties.getProperty( "SFTP_HOST" ) );

			client.authPassword( properties.getProperty( "SFTP_USERNAME" ), properties.getProperty( "SFTP_PASSWORD" ) );

			params.startTimeSftp();
			sftpClient = client.newSFTPClient();

			final String remoteFolder = properties.getProperty( "SFTP_HUBSPOT_FOLDER" );
			final String localFile = localFolder + UpdateProducts.ADPEARANCE_HUB_PRODUCT_LIST;

			sftpClient.get( remoteFolder + UpdateProducts.ADPEARANCE_HUB_PRODUCT_LIST, localFile );
			params.stopTimesFtp();
			client.close();
		}
		catch ( final IOException e )
		{
			final var errorMessage = "processCsvFilesInSftpFolder:" + e.getMessage();
			logJobMessage( params, errorMessage );
			e.printStackTrace();
		}
	}

	private ImpProductEntry createImpProductEntry( final Long jobId, final String[] csvLine )
	{
		final ImpProductEntry ret = new ImpProductEntry();

		ret.setJobId( jobId );
		ret.setProductID( csvLine[ 0 ] );
		ret.setProductName( csvLine[ 1 ] );
		ret.setProductImage( csvLine[ 2 ] );
		ret.setProductImageUrl( csvLine[ 3 ] );
		ret.setProductUrl( csvLine[ 4 ] );
		ret.setDescription( csvLine[ 5 ] );
		ret.setTruckSw( csvLine[ 6 ] );
		ret.setDropShipSw( csvLine[ 7 ] );
		ret.setRetailPrice( csvLine[ 8 ] );
		ret.setSalePrice( csvLine[ 9 ] );
		ret.setQtyOnHand( csvLine[ 10 ] );
		ret.setDiscountableSw( csvLine[ 11 ] );
		ret.setKaplanExclusive( csvLine[ 12 ] );
		ret.setNla( csvLine[ 13 ] );
		ret.setToBeNla( csvLine[ 14 ] );
		ret.setSecondDayUpCharge( csvLine[ 15 ] );
		ret.setNextDayUpCharge( csvLine[ 16 ] );
		ret.setInStockDate( csvLine[ 17 ] );
		ret.setPsAddDate( csvLine[ 18 ] );
		ret.setIsKaplanProduct( csvLine[ 19 ] );
		ret.setKaplanProductID( csvLine[ 20 ] );
		ret.setCost( csvLine[ 21 ] );
		ret.setIsbn( csvLine[ 22 ] );
		ret.setWeight( csvLine[ 23 ] );

		return ret;
	}

	private String getImpProductQuery()
	{
		final String sql = "INSERT INTO imp_products (job_id, product_id, product_name, product_image, product_image_url, product_url, description, "
			+ "truck_sw, drop_ship_sw, retail_price, sale_price, qty_on_hand, discountable_sw, kaplan_exclusive, nla, to_be_nla, second_day_up_charge, "
			+ "next_day_up_charge, in_stock_date, ps_add_date, is_kaplan_product, kaplan_product_id, product_cost, isbn, weight) "
			+ "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

		return sql;
	}

	private void insertImpProductEntry( final ProductParameters params, final ImpProductEntry entry )
	{
		String errorMessage;
		final String errorCode = null;

		final var sql = getImpProductQuery();

		try ( PreparedStatement statement = params.getConnection().prepareStatement( sql ); )
		{

			statement.setLong( 1, entry.getJobId() );
			statement.setString( 2, entry.getProductID() );
			final String trimmedProductId = entry.getProductID() != null ? entry
				.getProductID()
				.replaceFirst( "^0+(?!$)", "" ) : null;
			statement.setString( 2, trimmedProductId );
			statement.setString( 3, entry.getProductName() );
			statement.setString( 4, entry.getProductImage() );
			statement.setString( 5, entry.getProductImageUrl() );
			statement.setString( 6, entry.getProductUrl() );
			statement.setString( 7, entry.getDescription() );
			statement.setString( 8, entry.getTruckSw() );
			statement.setString( 9, entry.getDropShipSw() );
			statement.setString( 10, entry.getRetailPrice() );
			statement.setString( 11, entry.getSalePrice() );
			statement.setString( 12, entry.getQtyOnHand() );
			statement.setString( 13, entry.getDiscountableSw() );
			statement.setString( 14, entry.getKaplanExclusive() );
			statement.setString( 15, entry.getNla() );
			statement.setString( 16, entry.getToBeNla() );
			statement.setString( 17, entry.getSecondDayUpCharge() );
			statement.setString( 18, entry.getNextDayUpCharge() );
			statement.setString( 19, entry.getInStockDate() );
			statement.setString( 20, entry.getPsAddDate() );
			statement.setString( 21, entry.getIsKaplanProduct() );
			statement.setString( 22, entry.getKaplanProductID() );
			statement.setString( 23, entry.getCost() );
			statement.setString( 24, entry.getIsbn() );
			statement.setString( 25, entry.getWeight() );

			params.startTimeDatabase();
			statement.execute();
			params.stopTimeDatabase();
		}
		catch ( final SQLException e )
		{
			errorMessage = e.getMessage();
			System.out.println( "Error trying to get gh products:" + e.getMessage() );
			e.printStackTrace();
			logJobMessage( params, "main[ErrorCode:" + errorCode + ":" + errorMessage + "]" );
		}

	}

	private void handleProductCsvImport( final ProductParameters params, final Long jobId )
	{
		try
		{
			final String fileNameWithPath = localFolder + UpdateProducts.ADPEARANCE_HUB_PRODUCT_LIST;
			params.setCsvFileName( fileNameWithPath );
			final var insertsFilePath = params.getCsvFileName();

			// Create an object of filereader
			// class with CSV file as a parameter.
			final var filereader = new FileReader( insertsFilePath );

			// Build reader instance
			// Read data.csv
			// Default separator is pipe
			final CSVParser csvParser = new CSVParserBuilder()
				.withSeparator( '|' )
				.withQuoteChar( '"' )
				.withIgnoreQuotations( false )
				.build();

			// create csvReader object passing
			// file reader as a parameter
			// Start reading from line number 2 (line numbers start from zero)
			final CSVReader csvReader = new CSVReaderBuilder( filereader )
				.withCSVParser( csvParser )
				.withSkipLines( 1 )
				.build();

			String[] csvLine = new String[ 0 ];
			try
			{
				final var dataSource = createDataSource();

				String errorMessage;
				String errorCode = null;
				long impRowCount = 0;

				try ( var conn = dataSource.getConnection() )
				{
					params.setConnection( conn );
					while ( ( csvLine = csvReader.readNext() ) != null )
					{
						if ( csvLine != null )
						{
							final ImpProductEntry entry = createImpProductEntry( jobId, csvLine );

							insertImpProductEntry( params, entry );
							impRowCount++ ;
						}
					}
					logger.info( impRowCount + " hubspot product entries processed" );
				}
				catch ( final SQLException se )
				{
					errorMessage = se.getMessage();
					errorCode = String.valueOf( se.getErrorCode() );
					logJobMessage( params, "main[ErrorCode:" + errorCode + ":" + errorMessage + "]" );
					se.printStackTrace();
				}
			}
			catch ( final CsvValidationException e )
			{
				e.printStackTrace();
			}
		}
		catch ( final IOException e )
		{
			logJobMessage( params, e.getMessage() );
			logger.error( "handleProductCsvImport", e );
		}
	}

	private void importAllImpProducts( final ProductParameters params, final Long jobId )
	{
		// Get file from sftp and download locally
		handleHubspotProductCsvFile( params, jobId );

		// import files into db
		handleProductCsvImport( params, jobId );

		logger.info( "Csv Import complete" );
	}

	private String getDiffSql()
	{
		final String sql = "SELECT products.check_hubspot_differences(?) AS validMerge";
		return sql;
	}

	private void handleCheckDiffLogic( final ProductParameters params, final Long jobId )
	{
		String errorMessage;

		logger.info( "Checking for diff logic" );

		final String sql = getDiffSql();

		final var dataSource = createDataSource();

		try ( var conn = dataSource.getConnection() )
		{
			params.setConnection( conn );
			try ( PreparedStatement statement = params.getConnection().prepareStatement( sql ); )
			{
				statement.setLong( 1, jobId );

				params.startTimeDatabase();
				statement.execute();
				params.stopTimeDatabase();
			}
			catch ( final SQLException e )
			{
				errorMessage = e.getMessage();
				System.out.println( "Error trying to merge gh products:" + e.getMessage() );
				e.printStackTrace();
				logJobMessage( params, errorMessage );
			}
		}
		catch ( final SQLException se )
		{
			errorMessage = se.getMessage();
			logJobMessage( params, "main[" + errorMessage + "]" );
			se.printStackTrace();
		}

	}

	private void modifyProductDataForImport( final ProductParameters params, final Long jobId )
	{
		// Currentlty we do not get the product url, image, image url, or description for GH products.
		// Need to manually add those

		// check for differences for import
		handleCheckDiffLogic( params, jobId );
	}

	private String getKelcCreateSql( final Long jobId )
	{
		final String sql = "SELECT " + "regexp_replace(hp.product_name, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g')"
			+ " AS Name, " + "CONCAT ('KELC_','', TRIM(LEADING '0' FROM hp.product_id)) AS SKU, "
			+ "	regexp_replace(hp.description, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g') " + "AS description, \r\n"
			+ "	hp.product_image_url, hp.product_url, \r\n" + "	CASE \r\n" + "		WHEN hp.ps_add_date = '' THEN NULL\r\n"
			+ "		WHEN DATE(hp.ps_add_date) IS NULL THEN NULL\r\n"
			+ "		ELSE TO_CHAR(TO_DATE(ps_add_date, 'YYYY-MM-DD HH24:MI:SS'), 'DD-MM-YYYY')\r\n" + "	END AS date, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.drop_ship_sw = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.drop_ship_sw = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS drop_ship_sw, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.kaplan_exclusive = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.kaplan_exclusive = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS kaplan_exclusive, hp.second_day_up_charge, hp.next_day_up_charge, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.nla = 'N' THEN 'false'\r\n" + "		WHEN hp.nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS nla,\r\n" + "	CASE \r\n" + "		WHEN hp.to_be_nla = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.to_be_nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS to_be_nla, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.truck_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.truck_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n" + "	END AS truck_sw, hp.retail_price, hp.sale_price, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.discountable_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.discountable_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n"
			+ "	END AS discountable_sw, hp.product_cost, hp.isbn, hp.weight, hp.qty_on_hand, hp.product_id, hp.product_action, hp.job_id, hp.hubspot_id, 'KELC' AS business_unit \r\n"
			+ "FROM products.hubspot_products hp \r\n" + "WHERE job_id  = " + jobId + " \r\n"
			+ "	AND is_kaplan_product = 'Y'\r\n" + "	AND product_action = 'CREATED' \r\n" + "	AND hubspot_id IS NULL ";

		return sql;
	}

	private String getGhCreateSql( final Long jobId )
	{
		final String sql = "SELECT " + "regexp_replace(hp.product_name, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g')"
			+ " AS Name, " + "CONCAT ('GH_','', TRIM(LEADING '0' FROM hp.product_id)) AS SKU, "
			+ "regexp_replace(hp.description, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g') " + "AS description, "
			+ "hp.product_image_url, \r\n" + "	hp.product_url, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.ps_add_date = '' THEN NULL\r\n" + "		WHEN DATE(hp.ps_add_date) IS NULL THEN NULL\r\n"
			+ "		ELSE TO_CHAR(TO_DATE(ps_add_date, 'YYYY-MM-DD HH24:MI:SS'), 'DD-MM-YYYY')\r\n" + "	END AS date, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.drop_ship_sw = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.drop_ship_sw = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS drop_ship_sw, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.kaplan_exclusive = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.kaplan_exclusive = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS kaplan_exclusive, hp.second_day_up_charge, hp.next_day_up_charge, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.nla = 'N' THEN 'false'\r\n" + "		WHEN hp.nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS nla,\r\n" + "	CASE \r\n" + "		WHEN hp.to_be_nla = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.to_be_nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS to_be_nla, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.truck_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.truck_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n" + "	END AS truck_sw, hp.retail_price, hp.sale_price, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.discountable_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.discountable_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n"
			+ "	END AS discountable_sw, hp.product_cost, hp.isbn, hp.weight, hp.qty_on_hand, hp.product_id, hp.product_action, hp.job_id, hp.hubspot_id, 'Gryphon House' AS business_unit \r\n"
			+ "FROM products.hubspot_products hp \r\n" + "WHERE job_id  = " + jobId + " \r\n"
			+ "	AND is_kaplan_product = 'N'\r\n" + "	AND product_action = 'CREATED' \r\n" + "	AND hubspot_id IS NULL ";

		return sql;
	}

	private String getKelcUpdateSql( final Long jobId )
	{
		final String sql = "SELECT " + "regexp_replace(hp.product_name, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g')"
			+ " AS Name, " + "CONCAT ('KELC_','', TRIM(LEADING '0' FROM hp.product_id)) AS SKU, "
			+ "regexp_replace(hp.description, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g')" + " AS description, "
			+ "hp.product_image_url, \r\n" + "	hp.product_url, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.ps_add_date = '' THEN NULL\r\n" + "		WHEN DATE(hp.ps_add_date) IS NULL THEN NULL\r\n"
			+ "		ELSE TO_CHAR(TO_DATE(ps_add_date, 'YYYY-MM-DD HH24:MI:SS'), 'DD-MM-YYYY')\r\n" + "	END AS date, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.drop_ship_sw = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.drop_ship_sw = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS drop_ship_sw, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.kaplan_exclusive = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.kaplan_exclusive = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS kaplan_exclusive, hp.second_day_up_charge, hp.next_day_up_charge, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.nla = 'N' THEN 'false'\r\n" + "		WHEN hp.nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS nla,\r\n" + "	CASE \r\n" + "		WHEN hp.to_be_nla = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.to_be_nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS to_be_nla, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.truck_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.truck_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n" + "	END AS truck_sw, hp.retail_price, hp.sale_price, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.discountable_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.discountable_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n"
			+ "	END AS discountable_sw, hp.product_cost, hp.isbn, hp.weight, hp.qty_on_hand, hp.product_id, hp.product_action, hp.job_id, hp.hubspot_id, 'KELC' AS business_unit \r\n"
			+ "FROM products.hubspot_products hp \r\n" + "WHERE job_id  = " + jobId + " \r\n"
			+ "	AND is_kaplan_product = 'Y'\r\n" + "	AND product_action = 'UPDATE'";

		return sql;
	}

	private String getGhUpdateSql( final Long jobId )
	{
		final String sql = "SELECT " + "regexp_replace(hp.product_name, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g')"
			+ " AS Name, " + "CONCAT ('GH_','', TRIM(LEADING '0' FROM hp.product_id)) AS SKU, "
			+ "regexp_replace(hp.description, E'[^\\x01-\\x7F]+|[\\t\\r\\n\\x0A]', '', 'g')" + " AS description, "
			+ "hp.product_image_url, \r\n" + "	hp.product_url, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.ps_add_date = '' THEN NULL\r\n" + "		WHEN DATE(hp.ps_add_date) IS NULL THEN NULL\r\n"
			+ "		ELSE TO_CHAR(TO_DATE(ps_add_date, 'YYYY-MM-DD HH24:MI:SS'), 'DD-MM-YYYY')\r\n" + "	END AS date, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.drop_ship_sw = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.drop_ship_sw = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS drop_ship_sw, \r\n"
			+ "	CASE \r\n" + "		WHEN hp.kaplan_exclusive = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.kaplan_exclusive = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS kaplan_exclusive, hp.second_day_up_charge, hp.next_day_up_charge, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.nla = 'N' THEN 'false'\r\n" + "		WHEN hp.nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n"
			+ "	END AS nla,\r\n" + "	CASE \r\n" + "		WHEN hp.to_be_nla = 'N' THEN 'false'\r\n"
			+ "		WHEN hp.to_be_nla = 'Y' THEN 'true'\r\n" + "		ELSE ''\r\n" + "	END AS to_be_nla, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.truck_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.truck_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n" + "	END AS truck_sw, hp.retail_price, hp.sale_price, \r\n" + "	CASE \r\n"
			+ "		WHEN hp.discountable_sw = 'N' THEN 'false'\r\n" + "		WHEN hp.discountable_sw = 'Y' THEN 'true'\r\n"
			+ "		ELSE ''\r\n"
			+ "	END AS discountable_sw, hp.product_cost, hp.isbn, hp.weight, hp.qty_on_hand, hp.product_id, hp.product_action, hp.job_id, hp.hubspot_id, 'Gryphon House' AS business_unit \r\n"
			+ "FROM products.hubspot_products hp \r\n" + "WHERE job_id  = " + jobId + " \r\n"
			+ "	AND is_kaplan_product = 'N'\r\n" + "	AND product_action = 'UPDATE'";

		return sql;
	}

	private ResultSet queryDatabase( final String sql, final ProductParameters params, final CSVWriter csvWriter )
	{
		String errorMessage;
		String errorCode = null;

		ResultSet rs = null;

		try
		{
			logger.info( "getting gh products" );

			final var dataSource = createDataSource();

			try ( var conn = dataSource.getConnection() )
			{
				params.setConnection( conn );

				try ( final var statment = params.getConnection().createStatement() )
				{
					params.startTimeDatabase();
					rs = statment.executeQuery( sql );

					csvWriter.writeAll( rs, false );
					csvWriter.close();
					params.stopTimeDatabase();
				}
				catch ( final SQLException e )
				{
					System.out.println( "Error trying to query new csv:" + e.getMessage() );
					e.printStackTrace();
				}
			}
			catch ( final SQLException se )
			{
				errorMessage = se.getMessage();
				errorCode = String.valueOf( se.getErrorCode() );
				logJobMessage( params, "main[ErrorCode:" + errorCode + ":" + errorMessage + "]" );
				se.printStackTrace();
			}

		}
		catch ( final Exception e )
		{
			errorMessage = e.getMessage();
			logJobMessage( params, "main[" + errorMessage + "]" );
			e.printStackTrace();
		}

		return rs;

	}

	private void createCsvFile(	final ProductParameters params,
											final Long jobId,
											final String sql,
											final String staticFileName )
	{
		try
		{
			final String fileName = localFolder + jobId + staticFileName;
			final FileWriter fileWriter = new FileWriter( fileName );
			final CSVWriter csvWriter = new CSVWriter( fileWriter, '|', '"', '\\', "\n" );

			queryDatabase( sql, params, csvWriter );

		}
		catch ( final IOException e )
		{
			e.printStackTrace();
		}

	}

	private void createCsvFiles( final ProductParameters params, final Long jobId )
	{
		final String kelcCreateSql = getKelcCreateSql( jobId );
		createCsvFile( params, jobId, kelcCreateSql, UpdateProducts.HUBSPOT_KELC_CREATE_FILE_BASE );

		final String ghCreateSql = getGhCreateSql( jobId );
		createCsvFile( params, jobId, ghCreateSql, UpdateProducts.HUBSPOT_GH_CREATE_FILE_BASE );

		final String kelcUpdateSql = getKelcUpdateSql( jobId );
		createCsvFile( params, jobId, kelcUpdateSql, UpdateProducts.HUBSPOT_KELC_UPDATE_FILE_BASE );

		final String ghUpdateSql = getGhUpdateSql( jobId );
		createCsvFile( params, jobId, ghUpdateSql, UpdateProducts.HUBSPOT_GH_UPDATE_FILE_BASE );
	}

	private String getMergeSql()
	{
		final String sql = "SELECT products.check_hubspot_differences(?) AS validMerge";
		return sql;
	}

	private void mergeKelcToGh( final ProductParameters params, final Long jobId )
	{
		String errorMessage;

		logger.info( "Merging KELC into GH" );

		final String sql = getMergeSql();

		final var dataSource = createDataSource();
		try ( var conn = dataSource.getConnection() )
		{
			params.setConnection( conn );
			try ( PreparedStatement statement = params.getConnection().prepareStatement( sql ); )
			{
				statement.setLong( 1, jobId );

				params.startTimeDatabase();
				statement.execute();
				params.stopTimeDatabase();
			}
			catch ( final SQLException e )
			{
				errorMessage = e.getMessage();
				System.out.println( "Error trying to merge gh products:" + e.getMessage() );
				e.printStackTrace();
				logJobMessage( params, errorMessage );
			}
		}
		catch ( final SQLException se )
		{
			errorMessage = se.getMessage();
			logJobMessage( params, "main[" + errorMessage + "]" );
			se.printStackTrace();
		}
	}

	private void jobCleanUp( final ProductParameters params )
	{
		String errorMessage;
		String errorCode = null;

		try
		{
			logger.info( "cleaning old jobs" );

			final var dataSource = createDataSource();

			try ( var conn = dataSource.getConnection() )
			{
				params.setConnection( conn );

				final var sql = "select products.clean_old_jobs()";

				try ( final var statment = params.getConnection().createStatement() )
				{
					params.startTimeDatabase();
					statment.executeQuery( sql );
					params.stopTimeDatabase();
				}
				catch ( final SQLException e )
				{
					System.out.println( "Error trying to clean old jobs:" + e.getMessage() );
					e.printStackTrace();
				}
			}
			catch ( final SQLException se )
			{
				errorMessage = se.getMessage();
				errorCode = String.valueOf( se.getErrorCode() );
				logJobMessage( params, "main[ErrorCode:" + errorCode + ":" + errorMessage + "]" );
				se.printStackTrace();
			}

		}
		catch ( final Exception e )
		{
			errorMessage = e.getMessage();
			logJobMessage( params, "main[" + errorMessage + "]" );
			e.printStackTrace();
		}
	}

	public void handleUpdate( final ProductParameters params )
	{
		// step 1, clean local files
		localCleanup();

		// step 2, start job and get back job id
		final Long jobId = startJob( params );

		// step , sftp and grab hubspot-product-list.csv file and insert into "imp_products"
		importAllImpProducts( params, jobId );

		// step 5 handle data logic
		modifyProductDataForImport( params, jobId );

		// step 6 handle merging kelc items into gh
		mergeKelcToGh( params, jobId );

		// step 7 create final 4 queries to run
		createCsvFiles( params, jobId );

		// step 8 handle import (API call and temp CSV clear out)
		processCsvFilesInSftpFolder( params, jobId );

		// step 9
		jobCleanUp( params );
	}

	public static void main( final String[] args )
	{
		if ( args.length != 1 )
		{
			System.out.println( "Config file name has to be provided on command line and job id" );
			return;
		}
		final var configFileName = args[ 0 ];
		final var up = new UpdateProducts( configFileName );

		final ProductParameters params = new ProductParameters();
		up.logger.info( "Configuration File: " + configFileName );

		up.handleUpdate( params );
	}

}
