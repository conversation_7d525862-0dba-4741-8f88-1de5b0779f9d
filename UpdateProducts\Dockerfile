#Pulling base image designed for Azure Function to work with Java 17
FROM mcr.microsoft.com/azure-functions/java:4-java17

#Updating OS and installing Required packages
RUN apt-get update  && apt-get install -y \ 
    openjdk-17-jdk -y \
    openssh-server -y \
    cifs-utils -y

#Creating Working Directory
WORKDIR /HubspotUpdateProducts

#Copying configuration file, JAR file, EntryScript.sh file to the Working Directory
COPY ./hubspot-updateproducts-0.0.1-SNAPSHOT-spring-boot.jar /HubspotUpdateProducts
COPY ./EntryScript.sh /HubspotUpdateProducts

#Creating Temporary Folder for files
RUN mkdir -p /HubspotUpdateProducts/UpdateProducts
RUN mkdir -p /HubspotUpdateProducts/configFile
RUN mkdir -p /var/log/Kaplan

#Exposing Ports
EXPOSE 22
EXPOSE 443
EXPOSE 445
EXPOSE 80
EXPOSE 5432

#Adding execution permissions to Entryscript.sh
RUN chmod +x /HubspotUpdateProducts/EntryScript.sh

#Running HubspoProducts
ENTRYPOINT ["/HubspotUpdateProducts/EntryScript.sh"]