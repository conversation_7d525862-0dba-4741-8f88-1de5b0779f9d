/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.kaplanco.hubspot.products;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Collection;

/**
 *
 * <AUTHOR>
 */
public class HubSpotProducts
{
    private Integer total;
    private Collection<HubSpotProduct> results;
    private HubSpotPaging paging;

    public Integer getTotal()
    {
        return total;
    }

    public void setTotal(Integer total)
    {
        this.total = total;
    }

    public Collection<HubSpotProduct> getResults()
    {
        return results;
    }

    public void setResults(Collection<HubSpotProduct> results)
    {
        this.results = results;
    }

    public HubSpotPaging getPaging()
    {
        return paging;
    }

    public void setPaging(HubSpotPaging paging)
    {
        this.paging = paging;
    }
    
}
