<?xml version="1.0" encoding="UTF-8"?>
<!-- Extra logging related to initialization of Log4j. 
 Set to debug or trace if log4j initialization is failing. -->
<Configuration status="info">
	<Properties>
        <Property name="basePath">/var/log/Kaplan</Property>
        <!--  
        <Property name="basePath">/Users/<USER>/Downloads/HubSpot</Property>
        -->
    </Properties>
    <Appenders>
    	<RollingFile name="fileLogger" fileName="${basePath}/UpdateProducts.log" filePattern="${basePath}/UpdateProducts-%d{yyyy-MM-dd}.log">
            <PatternLayout>
                <pattern>[%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n</pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true" />
                <SizeBasedTriggeringPolicy size="10MB" />
            </Policies>
        </RollingFile>
        <!-- Console appender configuration  -->
        <Console name="console" target="SYSTEM_OUT">
            <PatternLayout
                pattern="%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n" />
        </Console>
    </Appenders>
    <Loggers>
    	<Root level="info" additivity="false">
            <AppenderRef ref="fileLogger"/>
        </Root>
    </Loggers>    
</Configuration>